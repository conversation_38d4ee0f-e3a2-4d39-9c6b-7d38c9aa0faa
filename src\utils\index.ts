import { API_DOMAIN, MAX_FILE_SIZE } from "@constants";
import { slugs } from "@constants/slugs";
import { buckets } from "@constants/buckets";

export const imageTypes = "image/jpeg, image/png, image/jpg";
export const pdfType = "application/pdf";

export const convertToSentenceCase = (text: string) => {
  const words = text
    ?.replace(/([a-z])([A-Z])/g, "$1 $2")
    ?.replaceAll("_", " ")
    ?.replaceAll("-", " ")
    ?.split(" ");
  const sentenceCaseWords = words?.map((word, index) =>
    index === 0 ? word?.charAt(0)?.toUpperCase() + word?.slice(1) : word
  );
  const sentenceCaseString = sentenceCaseWords?.join(" ");
  const sentence =
    sentenceCaseString?.length <= 3
      ? sentenceCaseString?.toUpperCase()
      : sentenceCaseString?.replace("Crm", "CRM");
  return sentence;
};

export const createImageURL = (
  path: string,
  bucketName: string,
  bucketKey: string
): string => {
  if (!!path) {
    const apiURL = "/api/images";
    const spllitedPath = path.split("/");
    const imageName = spllitedPath[spllitedPath.length - 1];
    const constructParentURL = `?bucketName=${bucketName}&bucketKey=${`${bucketKey}/${imageName}`}`;
    const finalImagePath = `${apiURL}/${imageName}${constructParentURL}`;
    return finalImagePath;
  } else {
    return "";
  }
};

export const getPocUrl = (slug: string | any): string => {
  switch (slug) {
    case slugs.carDamageDetection:
      return `${API_DOMAIN}/${slug}/detect`;
    case slugs.insuranceApplication:
      return `${API_DOMAIN}/api/v1/${slug}/verify`;
    case slugs.amd:
      return `${API_DOMAIN}/api/v1/amd`;
    default:
      return `${API_DOMAIN}/${slug}/extract`;
  }
};

export const getFileType = (fileType: string) => {
  return fileType === "application/pdf" ? "pdf" : "image";
};

const fileTypes = (isAMD: boolean): string[] => {
  let allowedFileFormats = [];
  if (isAMD) {
    allowedFileFormats = ["ulaw"];
  } else {
    allowedFileFormats = ["image/jpeg", "image/png", "image/jpg"];
  }
  return allowedFileFormats;
};

export const isPropertyExists = (
  obj: any,
  property: any,
  length: number
): boolean => {
  if (obj && typeof obj === "object" && obj !== null) {
    const objProperty = obj[property];
    if (Object.keys(obj).length > 0 && !!objProperty) {
      if (Array.isArray(objProperty)) {
        return objProperty.length > length;
      } else if (!!objProperty) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  } else {
    return false;
  }
};

export const isValidTargetObject = (obj: any): boolean => {
  return (
    obj &&
    typeof obj === "object" &&
    Object.keys(obj).length > 0 &&
    Object.keys(obj.target).length > 0
  );
};

export const isFileTypeAllowed = (
  fileType: string,
  isAMD: boolean
): boolean => {
  const formats = fileTypes(isAMD);
  return formats.includes(fileType);
};

export const isFileTypeNotAllowed = (
  fileType: string,
  isAMD: boolean
): boolean => {
  const formats = fileTypes(isAMD);
  return !formats.includes(fileType);
};

export const getFileUploadErrors = (
  filesArray: File[],
  isAMD: boolean
): { [key: string]: boolean } => {
  const hasExceededSizeLimit = filesArray.some(
    (file: File) => file.size > MAX_FILE_SIZE
  );
  const hasInvalidFileTypes = filesArray.some((file: File) =>
    isFileTypeNotAllowed(file.type, isAMD)
  );
  return {
    hasExceededSizeLimit,
    hasInvalidFileTypes,
  };
};

export const getFileExtension = (filename: string) => {
  const dotIndex = filename?.lastIndexOf(".");
  return dotIndex === -1 ? "" : filename?.slice(dotIndex);
};

export const getFileName = (filename: string, seperator: string) => {
  const seperatorIndex = filename?.lastIndexOf(seperator);
  return seperatorIndex === -1 ? "" : filename?.substring(0, seperatorIndex);
};

export const getTargetStatus = (isTrue: boolean | null | undefined): string => {
  if (isTrue === undefined || isTrue === null) {
    return "-";
  } else if (isTrue) {
    return '<i class="fas fa-circle-check check-icon"></i>';
  } else {
    return '<i class="fas fa-circle-xmark cross-icon"></i>';
  }
};

export const getSubtitle = (slug: string): { [x: string]: string } => {
  switch (slug) {
    case slugs.drivingLicense:
      return {
        subtitle:
          "Use the below button to upload image of your driving license and get the extracted data.",
        keyword: "driving license",
      };
    case slugs.certificateOfTitle:
      return {
        subtitle:
          "Use the below button to upload image of your certificate and get the extracted data.",
        keyword: "certificate",
      };
    case slugs.carDamageDetection:
      return {
        subtitle:
          "Use the below button to upload images of your damaged cars and get the extracted data.",
        keyword: "damaged cars",
      };
    case slugs.insuranceApplication:
      return {
        subtitle:
          "Use the below buttons to upload images of your insurance applications and get the extracted data.",
        keyword: "insurance application",
      };
    case slugs.amd:
      return {
        subtitle:
          "Use the below button to upload audio file and get the extracted data.",
        keyword: "audio file",
      };
    default:
      return { subtitle: "", keyword: "" };
  }
};

export const getBucketName = (slug: string) => {
  switch (slug) {
    case slugs.drivingLicense:
      return buckets.dlo;
    case slugs.certificateOfTitle:
      return buckets.cot;
    case slugs.carDamageDetection:
      return buckets.cdi;
    case slugs.insuranceApplication:
      return buckets.iua;
    case slugs.amd:
      return buckets.amd;
    default:
      return "";
  }
};
