import { InsuranceApplicationResponse } from "../../typings";

export interface ApplicationDataProps {
  data: InsuranceApplicationResponse;
  hasResult: boolean;
}
export enum tab {
  insured = "insured_information",
  policy = "policy_information",
  payment = "payment_information",
  business = "line_of_business",
  driver = "driver_information",
  vehicle = "vehicle_information",
  coverage = "coverage_information",
  company = "company_information",
  signature = "signature",
  p2p = "promise_to_provide_information",
}
