import { Sample } from "@typings";

export interface SamplesGridProps {
  name: string;
  handleFile?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  samples: Sample[];
  isMi?: boolean;
  isAMD?: boolean;
  sampleIndex: number;
  selectFile: (sample: Sample, index: number) => void;
  areMultipleFilesAllowed: boolean;
  fileInputRef: React.RefObject<HTMLInputElement>;
  handleDownloadSample: (sample: Sample) => Promise<void>;
  handleZipFileDownload?: (folderName: string | any) => void;
}
