{"data": [{"id": 1, "name": "DLO", "src": "/assets/images/dlo/Driver License OCR.png", "title": "Driver License OCR", "slug": "driving-license", "subtitle": "Extract data from driver licenses across multiple layouts to automate operational processes.", "samples": [{"fileName": "california_license.jpg", "title": "California License", "path": "/assets/images/dlo/california_license.jpg", "absolutePath": "s3://ds-driver-license-ocr/sample-data/california_license.jpg"}, {"fileName": "washington_license.jpg", "title": "Washington License", "path": "/assets/images/dlo/washington_license.jpg", "absolutePath": "s3://ds-driver-license-ocr/sample-data/washington_license.jpg"}, {"fileName": "minnesota_license.jpg", "title": "Minnesota License", "path": "/assets/images/dlo/minnesota_license.jpg", "absolutePath": "s3://ds-driver-license-ocr/sample-data/minnesota_license.jpg"}]}, {"id": 2, "name": "COT", "src": "/assets/images/cot/Certificate Of Title OCR.png", "title": "Certificate Of Title OCR", "slug": "certificate-of-title", "subtitle": "Extract data from Title Certificate to automate underwriting process or verifying the authenticity of the vehicle ownership.", "samples": [{"fileName": "new_mexico_title.jpeg", "title": "New Mexico Title", "path": "/assets/images/cot/new_mexico_title.jpeg", "absolutePath": "s3://ds-certificate-of-title-ocr/sample-data/new_mexico_title.jpeg"}, {"fileName": "utah_title.jpg", "title": "Utah Title", "path": "/assets/images/cot/utah_title.jpg", "absolutePath": "s3://ds-certificate-of-title-ocr/sample-data/utah_title.jpg"}, {"fileName": "texas_title.jpg", "title": "Texas Title", "path": "/assets/images/cot/texas_title.jpg", "absolutePath": "s3://ds-certificate-of-title-ocr/sample-data/texas_title.jpg"}]}, {"id": 3, "name": "CDI", "src": "/assets/images/cdi/Car Damage Identification.png", "title": "Car Damage Identification", "slug": "car-damage-detection", "subtitle": "Automated data capture for car damage identification. Extract information from damaged images and automate workflows for related use cases.", "samples": [{"fileName": "Nissan Rogue Sport S.jpg", "folderName": "Nissan Rogue Sport S", "title": "Nissan Rogue Sport S", "paths": ["/assets/images/cdi/Nissan Rogue Sport S/nissan-front-leftside-view.jpg", "/assets/images/cdi/Nissan Rogue Sport S/nissan-front-rightside-view.jpg", "/assets/images/cdi/Nissan Rogue Sport S/nissan-frontside-view.jpg", "/assets/images/cdi/Nissan Rogue Sport S/nissan-interior1.jpg"], "absolutePaths": ["s3://ds-car-damage-identification/sample-data/Nissan Rogue Sport S/request/nissan-front-leftside-view.jpg", "s3://ds-car-damage-identification/sample-data/Nissan Rogue Sport S/request/nissan-front-rightside-view.jpg", "s3://ds-car-damage-identification/sample-data/Nissan Rogue Sport S/request/nissan-frontside-view.jpg", "s3://ds-car-damage-identification/sample-data/Nissan Rogue Sport S/request/nissan-interior1.jpg"]}, {"fileName": "Honda Civic VP.jpg", "folderName": "Honda Civic VP", "title": "Honda Civic VP", "paths": ["/assets/images/cdi/Honda Civic VP/honda-front-rightside-view.jpg", "/assets/images/cdi/Honda Civic VP/honda-frontside-view.jpg", "/assets/images/cdi/Honda Civic VP/honda-interior1.jpg", "/assets/images/cdi/Honda Civic VP/honda-rear-leftside-view.jpg"], "absolutePaths": ["s3://ds-car-damage-identification/sample-data/Honda Civic VP/request/honda-front-rightside-view.jpg", "s3://ds-car-damage-identification/sample-data/Honda Civic VP/request/honda-frontside-view.jpg", "s3://ds-car-damage-identification/sample-data/Honda Civic VP/request/honda-interior1.jpg", "s3://ds-car-damage-identification/sample-data/Honda Civic VP/request/honda-rear-leftside-view.jpg"]}, {"fileName": "Ford F250 Super Duty.jpg", "folderName": "Ford F250 Super Duty", "title": "Ford F250 Super Duty", "paths": ["/assets/images/cdi/Ford F250 Super Duty/ford-front-rightside-view.jpg", "/assets/images/cdi/Ford F250 Super Duty/ford-frontside-view.jpg", "/assets/images/cdi/Ford F250 Super Duty/ford-interior1.jpg", "/assets/images/cdi/Ford F250 Super Duty/ford-rear-leftside-view.jpg"], "absolutePaths": ["s3://ds-car-damage-identification/sample-data/Ford F250 Super Duty/request/ford-front-rightside-view.jpg", "s3://ds-car-damage-identification/sample-data/Ford F250 Super Duty/request/ford-frontside-view.jpg", "s3://ds-car-damage-identification/sample-data/Ford F250 Super Duty/request/ford-interior1.jpg", "s3://ds-car-damage-identification/sample-data/Ford F250 Super Duty/request/ford-rear-leftside-view.jpg"]}]}, {"id": 4, "name": "IUA", "src": "/assets/images/iua/Insurance Underwriting Automation.png", "title": "Insurance Underwriting Automation", "slug": "insurance-application", "subtitle": "Automated process to extract relevant information and take underwriting decisions within few seconds.", "samples": [{"fileName": "Insurance Application.jpeg", "title": "Insurance Application", "folderName": "Insurance Application", "paths": ["/assets/images/iua/sample1/driving_license.jpg", "/assets/images/iua/sample1/itc.png", "/assets/images/iua/sample1/crm_receipt.png", "/assets/images/iua/sample1/application.png", "/assets/images/iua/sample1/motor_vehicle_record.png", "/assets/images/iua/sample1/broker_package.png"], "documentPaths": {"driving_license": "s3://ds-insurance-underwriting-automation/sample-data/driving_license.jpg", "itc": "s3://ds-insurance-underwriting-automation/sample-data/itc.pdf", "crm_receipt": "s3://ds-insurance-underwriting-automation/sample-data/crm_receipt.pdf", "application": "s3://ds-insurance-underwriting-automation/sample-data/application.pdf", "motor_vehicle_record": "s3://ds-insurance-underwriting-automation/sample-data/motor_vehicle_record.pdf", "broker_package": "s3://ds-insurance-underwriting-automation/sample-data/broker_package.pdf", "pleasure_use_letter": "s3://ds-insurance-underwriting-automation/sample-data/pleasure_use_letter.pdf", "artisan_use_letter": "s3://ds-insurance-underwriting-automation/sample-data/artisan_use_letter.pdf", "eft": "s3://ds-insurance-underwriting-automation/sample-data/eft.pdf", "non_owners_letter": "s3://ds-insurance-underwriting-automation/sample-data/non_owners_letter.pdf", "stripe_receipt": "s3://ds-insurance-underwriting-automation/sample-data/stripe_receipt.pdf", "registration": "s3://ds-insurance-underwriting-automation/sample-data/registration.jpg", "vehicle_record": "s3://ds-insurance-underwriting-automation/sample-data/vehicle_record.pdf", "promise_to_provide": "s3://ds-insurance-underwriting-automation/sample-data/promise_to_provide.pdf"}}]}, {"id": 5, "name": "AMD", "src": "/assets/images/amd/Answering Machine Detection.png", "title": "Answering Machine Detection", "slug": "amd", "subtitle": "Detect answering machine audio within a second.", "samples": [{"fileName": "answering_machine.ulaw", "title": "Answering Machine", "path": "/assets/images/amd/sample.png", "absolutePath": "s3://ds-answering-machine-detection/sample-data/answering_machine.ulaw"}, {"fileName": "fax.ulaw", "title": "Fax", "path": "/assets/images/amd/sample.png", "absolutePath": "s3://ds-answering-machine-detection/sample-data/fax.ulaw"}, {"fileName": "human.ulaw", "title": "Human", "path": "/assets/images/amd/sample.png", "absolutePath": "s3://ds-answering-machine-detection/sample-data/human.ulaw"}]}]}