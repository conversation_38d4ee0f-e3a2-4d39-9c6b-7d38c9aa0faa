import { DeleteObjectCommand, S3Client } from "@aws-sdk/client-s3";
import formidable from "formidable";
import { s3Values } from "@constants/s3";
import { getBucketName } from "@utils";
import { NextApiRequest, NextApiResponse } from "next";

interface RequestProps extends NextApiRequest {
  slug: string | any;
  userId: string | any;
}

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: RequestProps, res: NextApiResponse) {
  if (req.method !== "DELETE") {
    res.status(405).json({ message: "Method not allowed" });
  }
  const form = formidable({
    multiples: false,
  });

  form.parse(req, async (err, fields, files) => {
    if (err) {
      res.status(500).json({ message: "Error parsing the form data" });
      return;
    }

    const slug = Array.isArray(fields.slug) ? fields.slug[0] : fields.slug;
    const userId = Array.isArray(fields.userId)
      ? fields.userId[0]
      : fields.userId;
    const Key = Array.isArray(fields.Key) ? fields.Key[0] : fields.Key;

    if (!slug || !userId || !Key) {
      res.status(400).json({ message: "Missing required fields" });
      return;
    }
    const bucketName = getBucketName(slug as string);

    const s3 = new S3Client({
      region: s3Values.region,
      endpoint: `https://s3.${s3Values.region}.amazonaws.com`,
    });

    try {
      const params = {
        Bucket: bucketName,
        Key,
        SSECustomerAlgorithm: "AES256",
        SSECustomerKey: s3Values.sseCustomerKey,
      };
      const command = new DeleteObjectCommand(params);
      await s3.send(command);
      res.status(204).json({ message: "File removed successfully" });
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Error removing the file to S3" });
    }
  });
}
