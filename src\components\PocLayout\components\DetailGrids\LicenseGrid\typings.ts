interface Details {
  name: string;
  eye_color: string;
  gender: string;
  hair_color: string;
  height: string;
  weight: string;
  license_class: string;
  license_number: string;
  expiry_date: string;
  date_of_birth: string;
  issue_date: string;
  address: {
    street: string;
    city: string;
    state: string;
    zip_code: string;
  };
}
export interface LicenseGridProps {
  details: Details;
}
