export interface Sample {
  path?: string;
  sampleInd?: number;
  paths?: string[];
  title?: string;
  absolutePath?: string;
  absolutePaths?: string[];
  pdfAbsolutePath?: string;
  fileName?: string | any;
  folderName?: string;
  documentPaths?: {
    driving_license: string;
    itc: string;
    crm_receipt: string;
    application: string;
    motor_vehicle_record: string;
    broker_package: string;
    pleasure_use_letter?: string | any;
    artisan_use_letter?: string | any;
    eft?: string | any;
    non_owners_letter?: string | any;
    stripe_receipt?: string | any;
    registration?: string | any;
    vehicle_record?: string | any;
    promise_to_provide?: string | any;
  };
}
export interface SampleState extends Sample {
  index?: number;
}
export interface ErrorState {
  error: boolean;
  message: string;
}
