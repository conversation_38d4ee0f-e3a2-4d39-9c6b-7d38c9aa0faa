import React, { Fragment, useState } from "react";
import { CertificateGridProps } from "./typings";
import { motion } from "framer-motion";
import { Arrow } from "@public/assets/icons";
import { getTargetStatus } from "@utils";

const CommonFlexContainer: React.FC<{
  title: string;
  value: string | boolean;
}> = ({ title, value }) => {
  return (
    <div className="flex justify-between items-center py-3">
      <span className="flex-1 text-gradient-black font-semibold">{title}</span>
      <span
        className="flex-1 text-gradient-gray"
        dangerouslySetInnerHTML={{
          __html:
            typeof value === "boolean" ? getTargetStatus(value) : value || "NA",
        }}
      />
    </div>
  );
};
const CertificateGrid: React.FC<CertificateGridProps> = ({ details }) => {
  const [currentIndex, setCurrentIndex] = useState<number>(0);

  const handlePreviousIndex = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    if (currentIndex !== 0) {
      setCurrentIndex((prevDriverIndex: number) => prevDriverIndex - 1);
    }
  };

  const handleNextIndex = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    if (currentIndex !== details?.lien_holder?.length - 1) {
      setCurrentIndex((prevDriverIndex: number) => prevDriverIndex + 1);
    }
  };
  return (
    <div className="flex flex-col divide-y-2 divide-brand-white/90">
      <CommonFlexContainer title="Title No:" value={details?.title_no} />
      <CommonFlexContainer title="VIN:" value={details?.vin} />
      <CommonFlexContainer title="Year:" value={details?.year} />
      <CommonFlexContainer title="Make:" value={details?.make} />
      <CommonFlexContainer title="Model:" value={details?.model} />
      <CommonFlexContainer title="Body Style:" value={details?.body_style} />
      <CommonFlexContainer title="Issue Date:" value={details?.issue_date} />
      <CommonFlexContainer title="Title Type:" value={details?.title_type} />
      <CommonFlexContainer
        title="License Plate:"
        value={details?.license_plate}
      />
      <CommonFlexContainer
        title="Black and white?:"
        value={details?.is_black_and_white}
      />
      <CommonFlexContainer
        title="Titled state:"
        value={details?.titled_state}
      />
      <div className="divide-y-2 divide-brand-white/90 shadow px-4 rounded-lg">
        <h3 className="text-gradient-black text-xl py-4 font-bold">Odometer</h3>
        <CommonFlexContainer
          title="Reading:"
          value={details?.odometer?.reading}
        />
        <CommonFlexContainer title="Brand:" value={details?.odometer?.brand} />
      </div>
      <div className="divide-y-2 divide-brand-white/90 shadow px-4 my-4 rounded-lg">
        <div className="flex items-center justify-between flex-wrap">
          <h3 className="text-gradient-black text-xl py-4 font-bold">
            Lien Holder
          </h3>
          {details?.lien_holder?.length > 1 && (
            <div className="flex gap-x-4">
              <motion.button
                className="flex items-center justify-center w-8 text-white h-8 bg-brand-secondary/70 rounded-full navigation-left focus:outline-none border-0 disabled:bg-gray-200 pointer-events-auto"
                onClick={handlePreviousIndex}
                initial={{ opacity: 0, scale: 1 }}
                animate={{ opacity: 1 }}
                whileTap={currentIndex !== 0 ? { scale: 0.8 } : {}}
                transition={{ duration: 0.2 }}
                disabled={currentIndex === 0}
              >
                <Arrow />
              </motion.button>
              <motion.button
                className="flex items-center justify-center w-8 h-8 text-white bg-brand-secondary/70 rounded-full -translate-y-1/2 navigation-right focus:outline-none border-0 disabled:bg-gray-200 pointer-events-auto"
                onClick={handleNextIndex}
                initial={{ opacity: 0, scale: 1 }}
                animate={{ opacity: 1 }}
                whileTap={
                  currentIndex !== details?.lien_holder?.length - 1
                    ? { scale: 0.8 }
                    : {}
                }
                transition={{ duration: 0.2 }}
                disabled={currentIndex === details?.lien_holder?.length - 1}
              >
                <Arrow />
              </motion.button>
            </div>
          )}
        </div>
        {details?.lien_holder?.length > 0 ? (
          details?.lien_holder
            ?.slice(currentIndex, currentIndex + 1)
            .map((data: any, index: number) => (
              <Fragment key={index}>
                <CommonFlexContainer title="Name:" value={data?.name} />
                <CommonFlexContainer
                  title="Lien Date:"
                  value={data?.lien_date}
                />
                <div className="divide-y-2 divide-brand-white/90 px-6 shadow mb-4 mt-1 rounded-lg">
                  <div className="text-gradient-black text-xl font-bold py-4">
                    Address
                  </div>
                  <CommonFlexContainer
                    title="Street:"
                    value={data?.address?.street}
                  />
                  <CommonFlexContainer
                    title="City:"
                    value={data?.address?.city}
                  />
                  <CommonFlexContainer
                    title="State:"
                    value={data?.address?.state}
                  />
                  <CommonFlexContainer
                    title="Zip Code:"
                    value={data?.address?.zip_code}
                  />
                </div>
              </Fragment>
            ))
        ) : (
          <div className="py-4">No Lien Holder details available</div>
        )}
      </div>
      <div className="divide-y-2 divide-brand-white/90 shadow px-4 rounded-lg">
        <h3 className="text-gradient-black text-xl py-4 font-bold">Owner</h3>
        <div className="flex justify-between items-center py-3">
          <span className="flex-1 text-gradient-black font-semibold">
            Names:
          </span>
          {details && details.owner && details.owner.names.length > 0 ? (
            <ol className="flex-1 text-gradient-gray list-decimal">
              {details.owner.names.map((name: string, index: number) => (
                <li key={index}>{name}</li>
              ))}
            </ol>
          ) : (
            <span className="flex-1 text-gradient-gray">NA</span>
          )}
        </div>
        <CommonFlexContainer
          title="Ownership type:"
          value={details?.owner?.ownership_type}
        />
        <div className="divide-y-2 divide-brand-white/90 px-6 shadow mb-4 mt-1 rounded-lg">
          <div className="text-gradient-black text-xl font-bold py-4">
            Address
          </div>
          <CommonFlexContainer
            title="Street:"
            value={details?.owner?.address?.street}
          />
          <CommonFlexContainer
            title="City:"
            value={details?.owner?.address?.city}
          />
          <CommonFlexContainer
            title="State:"
            value={details?.owner?.address?.state}
          />
          <CommonFlexContainer
            title="Zip Code:"
            value={details?.owner?.address?.zip_code}
          />
        </div>
      </div>
    </div>
  );
};

export default CertificateGrid;
