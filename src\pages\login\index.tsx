import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { Raleway } from "next/font/google";
import * as Yup from "yup";
import { useFormik } from "formik";
import cx from "classnames";
import {
  EMAIL_REGEX,
  EMPTY_EMAIL,
  EMPTY_PASSWORD,
  INVALID_EMAIL,
  INVALID_PASSWORD,
} from "@constants";
import { Loader } from "@components";
import { useRouter } from "next/router";
import { slugs } from "@constants/slugs";
import Head from "next/head";
import { useAuthState } from "@contexts/AuthContext";
const raleway = Raleway({
  display: "swap",
  weight: ["500"],
  subsets: ["latin"],
});
interface LoginForm {
  email: string;
  password: string;
}

const LoginPage: React.FC = () => {
  const { loading, loginUser, error, setError } = useAuthState();
  const [loginLoader, setLoginLoader] = useState<boolean>(false);
  const router = useRouter();
  const { values, errors, touched, handleSubmit, handleChange } =
    useFormik<LoginForm>({
      initialValues: {
        email: "",
        password: "",
      },
      validationSchema: Yup.object({
        email: Yup.string()
          .required(EMPTY_EMAIL)
          .matches(EMAIL_REGEX, INVALID_EMAIL),
        password: Yup.string()
          .min(8, INVALID_PASSWORD)
          .required(EMPTY_PASSWORD),
      }),
      onSubmit: (values: LoginForm) => {
        handleLogin(values);
      },
    });

  const handleLogin = async (formValues: any) => {
    const { email, password } = formValues;
    setError("");
    setLoginLoader(true);
    await loginUser(email, password);
    setLoginLoader(false);
  };

  useEffect(() => {
    if (router.isReady) {
      if (localStorage.getItem("userId")) {
        router.replace(slugs.home);
      }
    }
  }, []);

  if (loading) {
    return <Loader />;
  } else {
    return (
      <>
        <Head>
          <title>Login | Maruti Techlabs</title>
        </Head>
        <motion.div
          className="flex h-[100dvh] overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          <div
            className="relative items-center bg-white justify-center px-8 hidden md:flex overflow-hidden"
            style={{
              width: "calc(100vw - 600px)",
            }}
          >
            <motion.img
              className="absolute top-0 -left-20"
              src="/assets/images/login-image.svg"
              initial={{ y: -500, rotate: 180 }}
              animate={{ y: 0 }}
              transition={{ duration: 0.5 }}
            />
            <motion.img
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              src="/assets/images/logo-black.svg"
              transition={{ duration: 0.3 }}
            />
            <motion.img
              className="absolute bottom-0 -right-14"
              src="/assets/images/login-image.svg"
              initial={{ y: 500 }}
              animate={{ y: 0 }}
              transition={{ duration: 0.5 }}
            />
          </div>
          <div className="flex flex-col items-start md:items-center space-y-8 justify-center bg-brand-white w-full md:w-[600px] px-16 md:px-8">
            <motion.div className="relative flex flex-col space-y-1 w-full md:w-[350px]">
              <motion.h1
                className="gradient-text"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
              >
                Welcome
              </motion.h1>
              <motion.span
                className={`${raleway.className} text-lg font-medium`}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
              >
                Please login to your account
              </motion.span>
              <form
                onSubmit={handleSubmit}
                className={`${raleway.className} flex flex-col space-y-7 py-6`}
              >
                <motion.div
                  className="relative flex flex-col space-y-3"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                >
                  <div className="flex items-center space-x-3">
                    <label htmlFor="email" className="text-base font-medium">
                      Email
                    </label>
                    {errors.email && touched.email && (
                      <motion.span
                        className="absolute left-12 text-sm text-brand-secondary max-w-[220px]"
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                      >
                        {errors.email}
                      </motion.span>
                    )}
                  </div>
                  <input
                    id="email"
                    type="email"
                    value={values.email}
                    onChange={handleChange}
                    className={cx(
                      "bg-white p-[10px] rounded-[3px] text-base focus:outline-brand-secondary border",
                      {
                        "border-brand-secondary": errors.email && touched.email,
                      }
                    )}
                    placeholder="Enter email"
                  />
                </motion.div>
                <motion.div
                  className="relative flex flex-col space-y-3"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                >
                  <div className="flex items-center space-x-3">
                    <label htmlFor="password" className="text-base font-medium">
                      Password
                    </label>
                    {errors.password && touched.password && (
                      <motion.span
                        className="absolute left-20 text-sm text-brand-secondary max-w-[220px]"
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                      >
                        {errors.password}
                      </motion.span>
                    )}
                  </div>
                  <input
                    id="password"
                    type="password"
                    value={values.password}
                    onChange={handleChange}
                    className={cx(
                      "bg-white p-[10px] rounded-[3px] text-base focus:outline-brand-secondary border",
                      {
                        "border-brand-secondary":
                          errors.password && touched.password,
                      }
                    )}
                    placeholder="Enter password"
                  />
                </motion.div>
                <motion.button
                  type="submit"
                  className="relative flex justify-center items-center px-[35px] py-[13px] text-white border border-brand-secondary bg-transparent rounded-[3px] font-semibold text-base btn-animate transform duration-300 overflow-hidden z-10 hover:text-brand-secondary focus:outline-none"
                  initial={{ opacity: 0, transitionDuration: "0.1s" }}
                  animate={{ opacity: 1, transitionDuration: "0.1s" }}
                >
                  {loginLoader ? <Loader classNames="sm" /> : "Login"}
                </motion.button>
              </form>
              {!!error && (
                <motion.span
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="absolute -bottom-5 text-brand-secondary"
                >
                  {error}
                </motion.span>
              )}
            </motion.div>
          </div>
        </motion.div>
      </>
    );
  }
};

export default LoginPage;
