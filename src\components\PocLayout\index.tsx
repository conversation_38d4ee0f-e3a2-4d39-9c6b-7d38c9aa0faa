import { <PERSON><PERSON><PERSON><PERSON>, Loader } from "@components";
import { motion } from "framer-motion";
import _ from "lodash";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { Raleway } from "next/font/google";
import {
  AMD_TOKEN,
  INSURANCE_TOKEN,
  MAX_FILE_SIZE,
  MAX_PAGES_LIMIT,
  SOMETHING_WENT_WRONG,
  UPLOAD_ERROR,
} from "@constants";
import {
  CertificateGrid,
  PocHeader,
  SamplesGrid,
  UploadGrid,
  LicenseGrid,
  AMDGrid,
  CarDamageDetectionGrid,
  InsuranceApplication,
} from "./components";
import { ErrorState, Sample, SampleState } from "@typings";
import { slugs } from "@constants/slugs";
import {
  createImageURL,
  getBucketName,
  getFileExtension,
  getFileUploadErrors,
  getPocUrl,
  isFileTypeAllowed,
  isFileTypeNotAllowed,
} from "@utils";
import { getNumberOfPages } from "@utils/pdfUtils";
import ErrorModal from "@components/Modal/ErrorModal";
import { CarDamageDetails } from "./components/DetailGrids/CarDamageDetectionGrid/typings";
import {
  DDocument,
  DocumentState,
  InsurancePaths,
  PocLayoutProps,
} from "./typings";
import { requiredProperties } from "./components/InsuranceApplication/documents";
import { buckets } from "@constants/buckets";

const raleway = Raleway({
  display: "swap",
  weight: ["400", "500", "600"],
  subsets: ["latin"],
});

const PocLayout: React.FC<PocLayoutProps> = ({ slug, keyword, subtitle }) => {
  const isCarDamageDetection = slug === slugs.carDamageDetection;
  const isInsuranceApplication = slug === slugs.insuranceApplication;
  const isAMD = slug === slugs.amd;
  const [data, setData] = useState<any>({});
  const [loading, setLoading] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [fileName, setFileName] = useState<string>("");
  const [sample, setSample] = useState<SampleState>({
    path: "",
    index: -1,
  });
  const [currentObjectIndex, setCurrentObjectIndex] = useState<number>(0);
  const [imageFile, setImageFile] = useState<string | boolean | any>("");
  const [imageFiles, setImageFiles] = useState<string[] | any>([]);
  const [imageLoader, setImageLoader] = useState<boolean>(false);
  const [uploadLoader, setUploadLoader] = useState<boolean>(false);
  const [documentHasError, setDocumentHasError] = useState<boolean>(false);
  const [details, setDetails] = useState<any>(null);
  const [currentFilePath, setCurrentFilePath] = useState<string>("");
  const [isUploadResult, setIsUploadResult] = useState<boolean>(false);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [storedInsurancePaths, setStoredInsurancePaths] = useState<
    InsurancePaths | any
  >({});
  const [selectedInsuranceFiles, setSelectedInsuranceFiles] = useState<
    DocumentState[]
  >([]);
  const [insuranceFileUploadIds, setInsuranceFileUploadIds] = useState<
    string[]
  >([]);
  const [fetchError, setFetchError] = useState<ErrorState>({
    error: false,
    message: "",
  });
  const [uploadError, setUploadError] = useState<ErrorState>({
    error: false,
    message: "",
  });
  const [QAError, setQAError] = useState<ErrorState>({
    error: false,
    message: "",
  });

  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isQAModalOpen, setIsQAModalOpen] = useState<boolean>(false);

  const dragTimeout = useRef<HTMLDivElement>(null);
  let userId: string | any = "";
  const gridRef = useRef<HTMLDivElement>(null);
  const [gridHeight, setGridHeight] = useState<number>(1105);

  let bucketKey = "";

  const handleDragging = (dragging: boolean) => {
    setIsDragging(dragging);
  };

  useEffect(() => {
    if (window && typeof window !== undefined) {
      const storedUserId = window.localStorage.getItem("userId");
      userId = storedUserId;
    }
  }, []);

  useEffect(() => {
    async function getPageData() {
      try {
        setLoading(true);
        const response = await fetch(`/api/${slug}`, {
          method: "GET",
        });
        if (response) {
          const responseData = await response.json();
          setData(responseData);
        }
      } catch (error: any) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    }
    if (slug) {
      getPageData();
    }
  }, [slug]);

  const handleSample = (path: string | any, index: number) => {
    setSample({ path, index });
  };

  const getPocDetails = useCallback(async (file_paths: any) => {
    try {
      setCurrentObjectIndex(0);
      setImageFiles([]);
      setFetchError({ error: false, message: "" });
      setImageLoader(true);
      setIsUploadResult(false);
      setDetails(null);
      const requestBody = () => {
        if (isInsuranceApplication) {
          return JSON.stringify(file_paths);
        } else if (Array.isArray(file_paths)) {
          return JSON.stringify({ file_paths });
        } else {
          return JSON.stringify({ file_path: file_paths });
        }
      };
      const requestHeaders = () => {
        if (isInsuranceApplication) {
          return {
            Authorization: `Bearer ${INSURANCE_TOKEN}`,
          };
        } else if (isAMD) {
          return {
            Authorization: `Bearer ${AMD_TOKEN}`,
          };
        } else {
          return {};
        }
      };
      const body = requestBody();
      const response = await fetch(getPocUrl(slug), {
        method: "POST",
        body,
        headers: requestHeaders() as any,
      });

      if (response.ok) {
        const pocDetails = await response.json();
        if (isInsuranceApplication || isAMD) {
          setDetails(pocDetails);
        } else {
          setDetails(pocDetails?.data);
        }
        setIsUploadResult(true);
        if (isCarDamageDetection) {
          setCurrentFilePath(pocDetails?.data?.[0]?.["file_path"]);
          setImageFiles(
            pocDetails?.data?.map((path: CarDamageDetails) =>
              createImageURL(path?.file_path, buckets.cdi, bucketKey)
            )
          );
        }
      } else {
        const errorResponse = await response.json();
        throw new Error(errorResponse.message);
      }
    } catch (error: any) {
      setUploadError({ error: false, message: "" });
      setQAError({ error: false, message: "" });
      setFetchError({ error: true, message: error?.message });
      setIsModalOpen(true);
      setDocumentHasError(true);
      setIsUploadResult(false);
      setDetails(null);
      setSample({ path: "", index: -1 });
      setImageFile(null);
      setImageFiles([]);
    } finally {
      setImageLoader(false);
      setSelectedInsuranceFiles([]);
      setInsuranceFileUploadIds([]);
      setStoredInsurancePaths({});
    }
  }, []);
  const uploadFile = useCallback(async (file: File, isAMD: boolean) => {
    if (file) {
      if (
        isFileTypeAllowed(
          isAMD ? getFileExtension(file.name)?.replace(".", "") : file.type,
          isAMD
        )
      ) {
        try {
          setUploadLoader(true);
          const formData = new FormData();
          formData.append("file", file);
          formData.append("slug", String(slug));
          formData.append("userId", userId);
          await fetch("/api/upload", {
            method: "POST",
            body: formData,
          });
          const generatedFilePath = `s3://${getBucketName(
            slug as string
          )}/user-data/${userId}/${file.name}`;
          await getPocDetails(generatedFilePath);
        } catch (error: any) {
          setIsModalOpen(true);
          setUploadError({
            error: true,
            message: error.message || UPLOAD_ERROR,
          });
        } finally {
          setUploadLoader(false);
        }
      }
    }
  }, []);

  const uploadInsuranceFiles = useCallback(async (file: File, id: string) => {
    if (file) {
      if (isFileTypeAllowed(file.type, false)) {
        try {
          setUploadError({ error: false, message: "" });
          setUploadLoader(true);
          const formData = new FormData();
          formData.append("file", file);
          formData.append("slug", String(slug));
          formData.append("userId", userId);
          await fetch("/api/upload", {
            method: "POST",
            body: formData,
          });
          const generatedFilePath = `s3://${getBucketName(
            slug as string
          )}/user-data/${userId}/${file.name}`;
          setInsuranceFileUploadIds((prevIds: any) => [...prevIds, id]);
          setStoredInsurancePaths((prevFilePaths: InsurancePaths) => {
            return { ...prevFilePaths, [id]: generatedFilePath };
          });
        } catch (error: any) {
          setIsModalOpen(true);
          setUploadError({
            error: true,
            message: error.message || UPLOAD_ERROR,
          });
        } finally {
          setUploadLoader(false);
        }
      }
    }
  }, []);

  const uploadMultipleFiles = useCallback(async (files: FileList) => {
    const formData = new FormData();
    const filePaths: string[] = [];
    if (files) {
      try {
        setUploadLoader(true);
        formData.append("slug", String(slug));
        Array.from(files).forEach((file, index) => {
          const key = `user-data/${userId}/request/${file.name}`;
          formData.append(`file[${index}]`, file);
          formData.append("slug", String(slug));
          formData.append("userId", userId);
          formData.append("keys", key);
          const filePath = `s3://${getBucketName(slug as string)}/${key}`;
          filePaths.push(filePath);
        });

        const response = await fetch("/api/upload/multiple", {
          method: "POST",
          body: formData,
        });

        if (!response.ok) {
          throw new Error("Failed to upload files");
        }

        bucketKey = `user-data/${userId}/response`;
        await getPocDetails(filePaths);
      } catch (error: any) {
        console.log({ error });
        setIsModalOpen(true);
        setUploadError({ error: true, message: error.message });
      } finally {
        setUploadLoader(false);
      }
    }
  }, []);

  const selectImage = useCallback(
    async (sample: SampleState, index: number) => {
      const { path } = sample;
      setImageFile("");
      setFileName("");
      bucketKey = `sample-data/${sample?.folderName}/response`;
      handleSample(path, index);
      let absolutePath: any = "";
      if (isCarDamageDetection) {
        absolutePath = sample?.absolutePaths;
      } else {
        absolutePath = sample?.absolutePath;
      }
      await getPocDetails(absolutePath);
    },
    []
  );
  const warnUploadErrors = (file: File, isAMD: boolean): void => {
    const ext = isAMD ? "ulaw" : "JPG, JPEG and PNG";
    const format = isAMD ? " is" : "s are";
    const size = "25 MB";
    const maxSize = MAX_FILE_SIZE;
    const type = isAMD
      ? getFileExtension(file.name)?.replace(".", "")
      : file.type;
    if (file) {
      if (file.size > maxSize) {
        setUploadError({
          error: true,
          message: `File limit exceeded, only upload file upto ${size}`,
        });
        setIsModalOpen(true);
        setDocumentHasError(true);
      } else if (isFileTypeNotAllowed(type, isAMD)) {
        setUploadError({
          error: true,
          message: `Unsupported media type, only ${ext} format${format} supported!`,
        });
        setIsModalOpen(true);
        setDocumentHasError(true);
      }
    }
  };

  const warnUploadErrorsForMultipleFiles = (
    hasExceededSizeLimit: boolean,
    hasInvalidFileTypes: boolean,
    isPagesLimitExceeded: boolean
  ) => {
    const maxSize = "25 MB";
    const ext = "JPG, JPEG, and PNG formats are";
    if (isPagesLimitExceeded) {
      setUploadError({
        error: true,
        message: `One or more files have more than ${MAX_PAGES_LIMIT} pages.`,
      });
      setIsModalOpen(true);
    } else if (hasExceededSizeLimit) {
      setUploadError({
        error: true,
        message: `File limit exceeded, only upload files upto ${maxSize}`,
      });
      setIsModalOpen(true);
    } else if (hasInvalidFileTypes) {
      setUploadError({
        error: true,
        message: `Unsupported media type, only ${ext} supported`,
      });
      setIsModalOpen(true);
    }
  };
  const handleFile = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      event.preventDefault();
      const file = event.target.files && event.target.files[0];
      if (file) {
        warnUploadErrors(file, false);
      }
      if (
        file &&
        isFileTypeAllowed(file.type, false) &&
        file.size <= MAX_FILE_SIZE
      ) {
        const blobURL = URL.createObjectURL(file);
        handleSample("", -1);
        setImageFile(blobURL);
        setImageFiles([]);
        await uploadFile(file, false);
        if (fileInputRef && fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      }
    },
    []
  );

  const handleMultipleFiles = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>, isAMD: boolean) => {
      event.preventDefault();
      const files = event.target.files;
      if (files) {
        const filesArray = Array.from(files);
        const { hasExceededSizeLimit, hasInvalidFileTypes } =
          getFileUploadErrors(filesArray, isAMD);
        setUploadLoader(true);
        const totalPDFPages = (await getNumberOfPages(files)).totalPages;
        setUploadLoader(false);
        const isPagesLimitExceeded = totalPDFPages > MAX_PAGES_LIMIT;
        warnUploadErrorsForMultipleFiles(
          hasExceededSizeLimit,
          hasInvalidFileTypes,
          isPagesLimitExceeded
        );
        const maxSize = MAX_FILE_SIZE;
        let areValidFiles = false;

        areValidFiles = filesArray.every(
          (file: File) =>
            isFileTypeAllowed(file.type, isAMD) && file.size <= maxSize
        );
        if (areValidFiles) {
          const blobURLs = filesArray.map((file: File) =>
            URL.createObjectURL(file)
          );
          setImageFiles(blobURLs);
          handleSample("", -1);
          setImageFile("");
          await uploadMultipleFiles(files);
          if (fileInputRef && fileInputRef.current) {
            fileInputRef.current.value = "";
          }
        }
      }
    },
    []
  );

  const handleDragOver = useCallback(
    (event: React.DragEvent<HTMLDivElement | HTMLLabelElement>) => {
      event.preventDefault();
      clearTimeout(dragTimeout.current as any);
    },
    []
  );

  const handleDrop = useCallback(
    async (event: React.DragEvent<HTMLDivElement | HTMLLabelElement>) => {
      event.preventDefault();
      handleDragging(false);
      const file = event.dataTransfer.files[0];
      if (file) {
        warnUploadErrors(file, false);
      }
      if (
        file &&
        isFileTypeAllowed(file.type, false) &&
        file.size <= MAX_FILE_SIZE
      ) {
        const blobURL = URL.createObjectURL(file);
        setImageFile(blobURL);
        handleSample("", -1);
        setImageFiles([]);
        await uploadFile(file, false);
      }
    },
    []
  );
  const handleMultipleDrop = useCallback(
    async (
      event: React.DragEvent<HTMLDivElement | HTMLLabelElement>,
      isAMD: boolean
    ) => {
      event.preventDefault();
      handleDragging(false);
      const files = event.dataTransfer.files;
      if (files) {
        const filesArray = Array.from(files);
        const { hasExceededSizeLimit, hasInvalidFileTypes } =
          getFileUploadErrors(filesArray, isAMD);
        setUploadLoader(true);
        const totalPDFPages = (await getNumberOfPages(files)).totalPages;
        setUploadLoader(false);
        const isPagesLimitExceeded = totalPDFPages > MAX_PAGES_LIMIT;
        warnUploadErrorsForMultipleFiles(
          hasExceededSizeLimit,
          hasInvalidFileTypes,
          isPagesLimitExceeded
        );
        const maxSize = MAX_FILE_SIZE;
        let areValidFiles = false;

        areValidFiles = filesArray.every(
          (file: File) =>
            isFileTypeAllowed(file.type, isAMD) && file.size <= maxSize
        );
        if (areValidFiles) {
          const blobURLs = filesArray.map((file: File) =>
            URL.createObjectURL(file)
          );
          handleSample("", -1);
          setImageFile("");
          setImageFiles(blobURLs);
          await uploadMultipleFiles(files);
        }
      }
    },
    []
  );
  const displayPocDetails = (details: any) => {
    switch (slug) {
      case slugs.certificateOfTitle:
        return <CertificateGrid details={details} />;
      case slugs.drivingLicense:
        return <LicenseGrid details={details} />;
      case slugs.carDamageDetection:
        return (
          <CarDamageDetectionGrid
            details={details}
            filePath={currentFilePath}
          />
        );
      case slugs.amd:
        return <AMDGrid details={details} />;
      default:
    }
  };

  useEffect(() => {
    if (
      details &&
      !!details[currentObjectIndex] &&
      !!details[currentObjectIndex]["file_path"]
    ) {
      setCurrentFilePath(details[currentObjectIndex]["file_path"]);
    }
  }, [currentObjectIndex]);

  const selectInsuranceFile = useCallback(async (sample: Sample) => {
    setSelectedInsuranceFiles([]);
    setInsuranceFileUploadIds([]);
    setStoredInsurancePaths({});
    await getInsuranceDetails(sample);
  }, []);

  const hasAllRequiredInsuranceFiles =
    _.keys(storedInsurancePaths).length &&
    _.every(requiredProperties, (property) =>
      _.has(storedInsurancePaths, property)
    );

  const handleSelectedInsuranceFile = async (
    event: React.ChangeEvent<HTMLInputElement>,
    document: DDocument
  ) => {
    if (event && event.target && event.target.files) {
      setDocumentHasError(false);
      const file = event.target.files[0];
      warnUploadErrors(file, false);
      setSelectedInsuranceFiles([
        ...selectedInsuranceFiles,
        { ...document, file },
      ]);
      if (!documentHasError) {
        await uploadInsuranceFiles(file, document.id);
      }
    }
  };

  const getInsuranceDetails = async (sample?: Sample | any) => {
    if (sample) {
      if (_.keys(sample.documentPaths).length) {
        await getPocDetails(sample.documentPaths);
      }
    }
    if (_.keys(storedInsurancePaths).length) {
      await getPocDetails(storedInsurancePaths);
    }
  };
  const handleZipFileDownload = async (folderName: string | any) => {
    const saveName = folderName;
    const bucketName = getBucketName(slug as string);
    const prefix = isInsuranceApplication
      ? "sample-data"
      : `sample-data/${folderName}/request`;
    const downloadUrl = `/api/samples/download-zip?bucketName=${bucketName}&prefix=${prefix}&folderName=${saveName}`;
    try {
      setUploadLoader(true);
      const response = await fetch(downloadUrl);
      if (response.ok) {
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${saveName}.zip`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      } else {
        const errorResponse = await response.json();
        throw new Error(errorResponse?.message);
      }
    } catch (error: any) {
      setIsModalOpen(true);
      setUploadError({ error: true, message: error?.message });
    } finally {
      setUploadLoader(false);
    }
  };

  const handleDownloadSample = async (sample: Sample) => {
    const fileName = sample?.fileName;
    const bucketName = getBucketName(slug as string);
    const bucketKey = `sample-data/${fileName}`;
    try {
      setUploadError({ error: false, message: "" });
      setUploadLoader(true);
      const response = await fetch(
        `/api/samples/download-file?fileName=${fileName}&bucketName=${bucketName}&bucketKey=${bucketKey}`
      );
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", fileName);
      document.body.appendChild(link);
      link.click();
      link.parentNode!.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      console.error("Error downloading file:", error);
      setIsModalOpen(true);
      setUploadError({
        error: true,
        message: error?.message || SOMETHING_WENT_WRONG,
      });
    } finally {
      setUploadLoader(false);
    }
  };
  const resetInsuranceDocument = async (
    event: React.MouseEvent,
    id: string | any
  ) => {
    event.preventDefault();
    try {
      setUploadLoader(true);
      const filename = storedInsurancePaths[id]?.slice(
        storedInsurancePaths[id]?.lastIndexOf("/") + 1,
        storedInsurancePaths[id]?.length
      );
      const formData = new FormData();
      formData.append("slug", String(slug));
      formData.append("userId", userId);
      formData.append("Key", `user-data/${userId}${filename}`);
      await fetch("/api/remove", {
        method: "DELETE",
        body: formData,
      });
      setStoredInsurancePaths((prevPaths: InsurancePaths | any) => {
        delete prevPaths[id];
        return prevPaths;
      });
      setSelectedInsuranceFiles((prevFiles: DocumentState[] | any) => {
        const clonedFiles = prevFiles ? [...prevFiles] : selectedInsuranceFiles;
        return clonedFiles.filter(
          (clonedFile: DocumentState) => clonedFile.id !== id
        );
      });
      setInsuranceFileUploadIds((prevIds: string[] | any) => {
        const clonedIds = prevIds ? [...prevIds] : insuranceFileUploadIds;
        return clonedIds.filter((clonedId: string) => clonedId !== id);
      });
    } catch (error: any) {
      setIsModalOpen(true);
      setUploadError({ error: true, message: error?.message });
    } finally {
      setUploadLoader(false);
    }
  };

  const handleAMDFile = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      event.preventDefault();
      const file = event.target.files && event.target.files[0];
      if (file) {
        warnUploadErrors(file, true);
      }
      if (
        file &&
        isFileTypeAllowed(
          getFileExtension(file.name)?.replace(".", ""),

          true
        ) &&
        file.size <= MAX_FILE_SIZE
      ) {
        handleSample("", -1);
        setFileName(file.name);
        setImageFile(true);
        setImageFiles([]);
        await uploadFile(file, true);
        if (fileInputRef && fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      }
    },
    []
  );
  const handleAMDDrop = useCallback(
    async (event: React.DragEvent<HTMLDivElement | HTMLLabelElement>) => {
      event.preventDefault();
      handleDragging(false);
      const file = event.dataTransfer.files[0];
      if (file) {
        warnUploadErrors(file, true);
      }
      if (
        file &&
        isFileTypeAllowed(
          getFileExtension(file.name)?.replace(".", ""),

          true
        ) &&
        file.size <= MAX_FILE_SIZE
      ) {
        setFileName(file.name);
        handleSample("", -1);
        setImageFile(true);
        setImageFiles([]);
        await uploadFile(file, true);
      }
    },
    []
  );

  useEffect(() => {
    const handleResize = () => {
      if (gridRef && gridRef.current) {
        setGridHeight(gridRef.current.clientHeight);
      }
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [gridHeight]);

  const handleSelectMethod = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (isCarDamageDetection) {
      return handleMultipleFiles(event, false);
    } else if (isAMD) {
      return handleAMDFile(event);
    } else {
      return handleFile(event);
    }
  };
  const handleDropMethod = (
    event: React.DragEvent<HTMLDivElement | HTMLLabelElement>
  ) => {
    if (isCarDamageDetection) {
      return handleMultipleDrop(event, false);
    } else if (isAMD) {
      return handleAMDDrop(event);
    } else {
      return handleDrop(event);
    }
  };

  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <>
          <PocHeader
            src={data?.src}
            title={data?.title}
            subtitle={data?.subtitle}
          />
          {isInsuranceApplication ? (
            <InsuranceApplication
              subtitle={subtitle}
              samples={data?.samples}
              isUploadResult={isUploadResult}
              insuranceDetails={details}
              documentHasError={documentHasError}
              getInsuranceDetails={getInsuranceDetails}
              handleInsuranceFileDownload={handleZipFileDownload}
              handleSelectedInsuranceFile={handleSelectedInsuranceFile}
              hasAllRequiredInsuranceFiles={hasAllRequiredInsuranceFiles}
              insuranceFileUploadIds={insuranceFileUploadIds}
              resetInsuranceDocument={resetInsuranceDocument}
              selectInsuranceFile={selectInsuranceFile}
            />
          ) : (
            <motion.div
              className="flex flex-col space-y-12 mx-auto p-6 transform duration-200 lg:translate-y-32"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6 }}
            >
              <motion.div className="flex flex-col space-y-4 items-center text-brand-black">
                <h2 className="text-4xl font-semibold text-center">Try now!</h2>
                <p
                  className={`${raleway.className} max-w-xl text-center font-medium leading-relaxed`}
                >
                  {subtitle}
                </p>
              </motion.div>
              <motion.div
                className="relative bg-brand-white mx-auto shadow-md rounded-lg"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6 }}
              >
                <div className="grid lg:grid-cols-[644px_auto_424px] p-6 rounded-lg gap-5">
                  <SamplesGrid
                    handleFile={handleSelectMethod}
                    handleDownloadSample={handleDownloadSample}
                    sampleIndex={sample?.index as number}
                    name="file"
                    isAMD={isAMD}
                    samples={data?.samples}
                    selectFile={selectImage}
                    areMultipleFilesAllowed={isCarDamageDetection}
                    fileInputRef={fileInputRef}
                    handleZipFileDownload={handleZipFileDownload}
                  />
                  <motion.div
                    ref={gridRef}
                    className={`${raleway.className} order-2 col-span-2 lg:col-span-1 lg:order-none lg:row-span-2 bg-white p-6 rounded-xl overflow-y-scroll shadow-md c-scroll`}
                    style={{
                      minHeight: isCarDamageDetection ? "1105px" : "auto",
                      maxHeight: isCarDamageDetection
                        ? `${gridHeight + 10}px`
                        : "1105px",
                    }}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6 }}
                  >
                    <div className="flex flex-col space-y-2">
                      {details && Object.keys(details)?.length > 0 ? (
                        displayPocDetails(details)
                      ) : (
                        <>
                          <span className="font-semibold">
                            Your document will be shown here
                          </span>
                          <span className="text-grayish text-sm font-medium">
                            Your {keyword} details will be shown here and you
                            can also copy all the {keyword} details.
                          </span>
                        </>
                      )}
                    </div>
                  </motion.div>
                  <UploadGrid
                    samples={data?.samples}
                    areMultipleFilesAllowed={isCarDamageDetection}
                    dragTimeout={dragTimeout}
                    sampleIndex={sample?.index}
                    imageFile={imageFile}
                    imageFiles={imageFiles}
                    isAMD={isAMD}
                    handleFile={handleSelectMethod}
                    handleDragOver={handleDragOver}
                    handleDrop={handleDropMethod}
                    isDragging={isDragging}
                    handleDragging={handleDragging}
                    currentObjectIndex={currentObjectIndex}
                    handleCurrentObjectIndex={setCurrentObjectIndex}
                    fileInputRef={fileInputRef}
                    fileName={fileName}
                  />
                </div>
              </motion.div>
            </motion.div>
          )}
          {(imageLoader || uploadLoader) && <BookLoader />}
          <ErrorModal
            errorObj={fetchError}
            open={isModalOpen}
            setOpen={setIsModalOpen}
          />
          <ErrorModal
            errorObj={uploadError}
            open={isModalOpen}
            setOpen={setIsModalOpen}
          />
          <ErrorModal
            errorObj={QAError}
            open={isQAModalOpen}
            setOpen={setIsQAModalOpen}
          />
        </>
      )}
    </>
  );
};

export default PocLayout;
