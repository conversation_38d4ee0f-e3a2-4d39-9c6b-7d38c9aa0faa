import React from "react";
import { Arrow, Document } from "@public/assets/icons";
import { Raleway } from "next/font/google";
import { motion } from "framer-motion";
import cx from "classnames";
import Image from "next/image";
import { imageTypes } from "@utils";
import { UploadGridProps } from "./typings";

const raleway = Raleway({
  display: "swap",
  weight: ["400", "500", "600"],
  subsets: ["latin"],
});

const UploadGrid: React.FC<UploadGridProps> = ({
  samples,
  fileName,
  isAMD,
  currentObjectIndex,
  handleCurrentObjectIndex,
  sampleIndex,
  imageFile,
  imageFiles,
  handleFile,
  handleDragOver,
  handleDrop,
  isDragging,
  handleDragging,
  dragTimeout,
  fileInputRef,
  areMultipleFilesAllowed,
}) => {
  const handleDragEnter = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    handleDragging(true);
  };

  const handleDragLeave = () => {
    (dragTimeout.current as any) = setTimeout(() => {
      handleDragging(false);
    }, 200);
  };
  const viewUploadBox = (): boolean => {
    if (areMultipleFilesAllowed) {
      return sampleIndex === -1 && imageFiles?.length === 0;
    } else {
      return sampleIndex === -1 && !imageFile;
    }
  };
  const shouldViewUploadBox = viewUploadBox();
  const handlePreviousImage = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    if (currentObjectIndex !== 0) {
      handleCurrentObjectIndex(
        (prevObjectIndex: number) => prevObjectIndex - 1
      );
    }
  };
  const handleNextImage = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    if (currentObjectIndex !== imageFiles?.length - 1) {
      handleCurrentObjectIndex(
        (prevObjectIndex: number) => prevObjectIndex + 1
      );
    }
  };
  const renderUploadedImages = () => {
    if (shouldViewUploadBox) {
      return (
        <form encType="multipart/form-data" className="pointer-events-auto">
          <input
            ref={fileInputRef}
            type="file"
            id="file"
            name="file"
            className="hidden"
            multiple={areMultipleFilesAllowed}
            onChange={handleFile}
            accept={imageTypes}
          />
          <motion.label
            htmlFor="file"
            className={`${raleway.className} flex flex-col space-y-4 items-center justify-center max-w-[273px] focus:outline-none`}
            initial={{ scale: 1 }}
            transition={{ duration: 0.2 }}
            whileTap={{ scale: 0.9 }}
            whileHover={{ cursor: "pointer" }}
          >
            <Document />
            <span className="text-[26px] text-center text-grayish font-medium">
              Upload a file
            </span>
          </motion.label>
        </form>
      );
    } else if (isAMD) {
      return (
        <div
          className="flex gap-x-3 max-w-[350px] items-center bg-gray-100 px-4 py-2 rounded-lg text-gray-400 text-xl font-semibold shadow shadow-gray-200"
          title={fileName || samples[sampleIndex]?.fileName}
        >
          <i className="fas fa-volume-up"></i>
          <span className="truncate">
            {fileName || samples[sampleIndex]?.fileName}
          </span>
        </div>
      );
    } else {
      return (
        <Image
          src={
            imageFile ||
            imageFiles?.[currentObjectIndex] ||
            samples?.[sampleIndex]?.path
          }
          className="rounded-lg bshadow max-w-[604px] max-h-[790px] self-center mt-5 cursor-default"
          width={604}
          height={790}
          alt="Image"
          priority
        />
      );
    }
  };

  return (
    <motion.label
      className={cx(
        "relative col-span-2 flex items-center justify-center rounded-xl shadow-md",
        {
          "bg-brand-secondary/10 bshadow-secondary": isDragging,
          "bg-white": !isDragging,
          "h-[300px]": isAMD,
          "h-[830px]": !isAMD,
          "pointer-events-none":
            areMultipleFilesAllowed && imageFiles?.length > 1,
        }
      )}
      onDragEnter={shouldViewUploadBox ? handleDragEnter : () => {}}
      onDragLeave={shouldViewUploadBox ? handleDragLeave : () => {}}
      onDragOver={shouldViewUploadBox ? handleDragOver : () => {}}
      onDrop={shouldViewUploadBox ? handleDrop : () => {}}
      whileHover={
        (isAMD && sampleIndex !== -1) || (isAMD && imageFile)
          ? { cursor: "default" }
          : { cursor: "pointer" }
      }
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.6 }}
    >
      {imageFiles && imageFiles.length > 0 && (
        <span className="absolute top-5 right-5 text-xl font-bold text-gray-400">
          {currentObjectIndex + 1} of {imageFiles?.length}
        </span>
      )}
      {renderUploadedImages()}
      {areMultipleFilesAllowed && imageFiles?.length > 1 && (
        <>
          <motion.button
            className="flex items-center justify-center w-10 text-white h-10 bg-brand-secondary/70 rounded-full absolute z-[999] top-1/2 -left-4 -translate-y-1/2 navigation-left focus:outline-none border-0 disabled:bg-gray-200 pointer-events-auto"
            onClick={handlePreviousImage}
            initial={{ opacity: 0, scale: 1 }}
            animate={{ opacity: 1 }}
            whileTap={currentObjectIndex !== 0 ? { scale: 0.8 } : {}}
            transition={{ duration: 0.2 }}
            disabled={currentObjectIndex === 0}
          >
            <Arrow />
          </motion.button>
          <motion.button
            className="flex items-center justify-center w-10 h-10 text-white bg-brand-secondary/70 rounded-full absolute z-[999] top-1/2 -right-4 -translate-y-1/2 navigation-right focus:outline-none border-0 disabled:bg-gray-200 pointer-events-auto"
            onClick={handleNextImage}
            initial={{ opacity: 0, scale: 1 }}
            animate={{ opacity: 1 }}
            whileTap={
              currentObjectIndex !== imageFiles?.length - 1
                ? { scale: 0.8 }
                : {}
            }
            transition={{ duration: 0.2 }}
            disabled={currentObjectIndex === imageFiles?.length - 1}
          >
            <Arrow />
          </motion.button>
        </>
      )}
    </motion.label>
  );
};

export default UploadGrid;
