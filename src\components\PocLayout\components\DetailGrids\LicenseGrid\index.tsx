import React from "react";
import { LicenseGridProps } from "./typings";

const CommonFlexContainer: React.FC<{ [key: string]: string }> = ({
  title,
  value,
}) => {
  return (
    <div className="flex justify-between items-center py-3">
      <span className="flex-1 text-gradient-black font-semibold">{title}</span>
      <span className="flex-1 text-gradient-gray">{value || "NA"}</span>
    </div>
  );
};
const LicenseGrid: React.FC<LicenseGridProps> = ({ details }) => {
  return (
    <div className="flex flex-col divide-y-2 divide-brand-white/90">
      <CommonFlexContainer title="Name:" value={details?.name} />
      <CommonFlexContainer title="Eye color:" value={details?.eye_color} />
      <CommonFlexContainer title="Gender:" value={details?.gender} />
      <CommonFlexContainer title="Hair color:" value={details?.hair_color} />
      <CommonFlexContainer title="Height:" value={details?.height} />
      <CommonFlexContainer title="Weight:" value={details?.weight} />
      <CommonFlexContainer
        title="License class:"
        value={details?.license_class}
      />
      <CommonFlexContainer
        title="License number:"
        value={details?.license_number}
      />
      <CommonFlexContainer title="Expiry date:" value={details?.expiry_date} />
      <CommonFlexContainer
        title="Date of birth:"
        value={details?.date_of_birth}
      />
      <CommonFlexContainer title="Issue date:" value={details?.issue_date} />
      <div className="divide-y-2 divide-brand-white/90 shadow px-4 rounded-lg">
        <h3 className="text-gradient-black text-xl py-4 font-bold">Address</h3>
        <CommonFlexContainer title="Street:" value={details?.address?.street} />
        <CommonFlexContainer title="City:" value={details?.address?.city} />
        <CommonFlexContainer title="State:" value={details?.address?.state} />
        <CommonFlexContainer
          title="Zip Code:"
          value={details?.address?.zip_code}
        />
      </div>
    </div>
  );
};

export default LicenseGrid;
