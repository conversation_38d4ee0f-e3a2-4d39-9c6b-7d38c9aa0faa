import React from "react";
import { motion } from "framer-motion";
import { convertToSentenceCase, getTargetStatus } from "@utils";

interface DriverInformationProps {
  info: any;
  currentDriverIndex: number;
}

const DriverInformation: React.FC<DriverInformationProps> = ({
  info,
  currentDriverIndex,
}) => {
  return (
    <motion.div
      className="flex flex-col"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <span className="w-full bg-brand-secondary/10 text-brand-secondary/75 font-semibold text-lg rounded-tl-lg rounded-tr-lg px-4 py-3">
        Drivers
      </span>
      {info?.drivers
        ?.slice(currentDriverIndex, currentDriverIndex + 1)
        ?.map((driver: any, index: number) => (
          <div
            className="flex flex-col space-y-4 border border-t-0 p-4 rounded-br-lg rounded-bl-lg"
            key={index}
          >
            <div className="flex gap-x-4">
              <div className="flex flex-1 items-center justify-between border p-4 rounded-lg shadow-md">
                <span>ID</span>
                <span>{driver?.id || "-"}</span>
              </div>
              <div className="flex flex-[2] justify-between items-center border p-4 rounded-lg shadow-md">
                <span>Status</span>
                <span>{driver?.status || "-"}</span>
              </div>
            </div>
            <div className="shadow-md rounded-lg">
              <div className="bg-gray-100 p-4 rounded-tr-lg rounded-tl-lg">
                Source:{" "}
                <span className="font-semibold">
                  {convertToSentenceCase(driver?.name?.source) || "-"}
                </span>
              </div>
              <div className="flex p-2 border border-t-0 border-gray-100 rounded-bl-lg rounded-br-lg">
                <div className="flex-[0.3] font-semibold p-4">Name</div>
                <div className="flex-1 flex flex-col space-y-4 p-4">
                  <span className="mb-2 font-semibold">Target</span>
                  <div className="flex">
                    <span className="flex-1">Insurance application</span>
                    <span
                      className="flex-[0.1] text-center"
                      dangerouslySetInnerHTML={{
                        __html: getTargetStatus(
                          driver?.name?.target?.insurance_application
                        ),
                      }}
                    />
                  </div>
                  <div className="flex">
                    <span className="flex-1">CRM receipt</span>
                    <span
                      className="flex-[0.1] text-center"
                      dangerouslySetInnerHTML={{
                        __html: getTargetStatus(
                          driver?.name?.target?.crm_receipt
                        ),
                      }}
                    />
                  </div>
                  <div className="flex">
                    <span className="flex-1">ITC</span>
                    <span
                      className="flex-[0.1] text-center"
                      dangerouslySetInnerHTML={{
                        __html: getTargetStatus(driver?.name?.target?.itc),
                      }}
                    />
                  </div>
                  <div className="flex">
                    <span className="flex-1">Pleasure use letter</span>
                    <span
                      className="flex-[0.1] text-center"
                      dangerouslySetInnerHTML={{
                        __html: getTargetStatus(
                          driver?.name?.target?.pleasure_use_letter
                        ),
                      }}
                    />
                  </div>
                  <div className="flex">
                    <span className="flex-1">Broker package</span>
                    <span
                      className="flex-[0.1] text-center"
                      dangerouslySetInnerHTML={{
                        __html: getTargetStatus(
                          driver?.name?.target?.broker_package
                        ),
                      }}
                    />
                  </div>
                  <div className="flex">
                    <span className="flex-1">MVR</span>
                    <span
                      className="flex-[0.1] text-center"
                      dangerouslySetInnerHTML={{
                        __html: getTargetStatus(driver?.name?.target?.mvr),
                      }}
                    />
                  </div>
                  <div className="flex">
                    <span className="flex-1">Artisan use letter</span>
                    <span
                      className="flex-[0.1] text-center"
                      dangerouslySetInnerHTML={{
                        __html: getTargetStatus(
                          driver?.name?.target?.artisan_use_letter
                        ),
                      }}
                    />
                  </div>
                  <div className="flex">
                    <span className="flex-1">Non owners letter</span>
                    <span
                      className="flex-[0.1] text-center"
                      dangerouslySetInnerHTML={{
                        __html: getTargetStatus(
                          driver?.name?.target?.non_owners_letter
                        ),
                      }}
                    />
                  </div>
                  <div className="flex">
                    <span className="flex-1">Registration</span>
                    <span
                      className="flex-[0.1] text-center"
                      dangerouslySetInnerHTML={{
                        __html: getTargetStatus(
                          driver?.name?.target?.registration
                        ),
                      }}
                    />
                  </div>
                  <div className="flex">
                    <span className="flex-1">VR</span>
                    <span
                      className="flex-[0.1] text-center"
                      dangerouslySetInnerHTML={{
                        __html: getTargetStatus(driver?.name?.target?.vr),
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="shadow-md rounded-lg">
              <div className="bg-gray-100 p-4 rounded-tr-lg rounded-tl-lg">
                Source:{" "}
                <span className="font-semibold">
                  {convertToSentenceCase(driver?.date_of_birth?.source) || "-"}
                </span>
              </div>
              <div className="flex p-2 border border-t-0 border-gray-100 rounded-bl-lg rounded-br-lg">
                <div className="flex-[0.3] font-semibold p-4">
                  Date of birth
                </div>
                <div className="flex-1 flex flex-col space-y-4 p-4">
                  <span className="mb-2 font-semibold">Target</span>
                  <div className="flex">
                    <span className="flex-1">ITC</span>
                    <span
                      className="flex-[0.1] text-center"
                      dangerouslySetInnerHTML={{
                        __html: getTargetStatus(
                          driver?.date_of_birth?.target?.itc
                        ),
                      }}
                    />
                  </div>
                  <div className="flex">
                    <span className="flex-1">Insurance application</span>
                    <span
                      className="flex-[0.1] text-center"
                      dangerouslySetInnerHTML={{
                        __html: getTargetStatus(
                          driver?.date_of_birth?.target?.insurance_application
                        ),
                      }}
                    />
                  </div>
                  <div className="flex">
                    <span className="flex-1">MVR</span>
                    <span
                      className="flex-[0.1] text-center"
                      dangerouslySetInnerHTML={{
                        __html: getTargetStatus(
                          driver?.date_of_birth?.target?.mvr
                        ),
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="shadow-md rounded-lg">
              <div className="bg-gray-100 p-4 rounded-tr-lg rounded-tl-lg">
                Source:{" "}
                <span className="font-semibold">
                  {convertToSentenceCase(driver?.gender?.source) || "-"}
                </span>
              </div>
              <div className="flex p-2 border border-t-0 border-gray-100 rounded-bl-lg rounded-br-lg">
                <div className="flex-[0.3] font-semibold p-4">Gender</div>
                <div className="flex-1 flex flex-col space-y-4 p-4">
                  <span className="mb-2 font-semibold">Target</span>
                  <div className="flex">
                    <span className="flex-1">Insurance application</span>
                    <span
                      className="flex-[0.1] text-center"
                      dangerouslySetInnerHTML={{
                        __html: getTargetStatus(
                          driver?.gender?.target?.insurance_application
                        ),
                      }}
                    />
                  </div>
                  <div className="flex">
                    <span className="flex-1">ITC</span>
                    <span
                      className="flex-[0.1] text-center"
                      dangerouslySetInnerHTML={{
                        __html: getTargetStatus(driver?.gender?.target?.itc),
                      }}
                    />
                  </div>
                  <div className="flex">
                    <span className="flex-1">MVR</span>
                    <span
                      className="flex-[0.1] text-center"
                      dangerouslySetInnerHTML={{
                        __html: getTargetStatus(driver?.gender?.target?.mvr),
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="flex gap-x-4 items-center">
              <div className="flex flex-1 justify-between items-center border p-4 rounded-lg shadow-md">
                <span>Marital status</span>
                <span>{driver?.marital_status || "-"}</span>
              </div>
              <div className="flex flex-1 justify-between items-center border p-4 rounded-lg shadow-md">
                <span>Driving experience</span>
                <span>{driver?.driving_experience || "-"}</span>
              </div>
            </div>
            <div className="flex justify-between items-center border p-4 rounded-lg shadow-md">
              <span>SR filing</span>
              <span>{driver?.sr_filing || "-"}</span>
            </div>
            <div className="shadow-md rounded-lg">
              <div className="bg-gray-100 p-4 rounded-tr-lg rounded-tl-lg">
                Source:{" "}
                <span className="font-semibold">
                  {convertToSentenceCase(driver?.license_number?.source) || "-"}
                </span>
              </div>
              <div className="flex p-2 border border-t-0 border-gray-100 rounded-bl-lg rounded-br-lg">
                <div className="flex-[0.3] font-semibold p-4">
                  License number
                </div>
                <div className="flex-1 flex flex-col space-y-4 p-4">
                  <span className="mb-2 font-semibold">Target</span>
                  <div className="flex">
                    <span className="flex-1">Insurance application</span>
                    <span
                      className="flex-[0.1] text-center"
                      dangerouslySetInnerHTML={{
                        __html: getTargetStatus(
                          driver?.license_number?.target?.insurance_application
                        ),
                      }}
                    />
                  </div>
                  <div className="flex">
                    <span className="flex-1">MVR</span>
                    <span
                      className="flex-[0.1] text-center"
                      dangerouslySetInnerHTML={{
                        __html: getTargetStatus(
                          driver?.license_number?.target?.mvr
                        ),
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="flex justify-between items-center border p-4 rounded-lg shadow-md">
              <span>Number of violation</span>
              <span>{driver?.number_of_violation || "-"}</span>
            </div>
            <div className="shadow-md rounded-lg">
              <div className="bg-gray-100 p-4 rounded-tr-lg rounded-tl-lg">
                Source:{" "}
                <span className="font-semibold">
                  {convertToSentenceCase(driver?.relationship?.source) || "-"}
                </span>
              </div>
              <div className="flex p-2 border border-t-0 border-gray-100 rounded-bl-lg rounded-br-lg">
                <div className="flex-[0.3] font-semibold p-4">Relationship</div>
                <div className="flex-1 flex flex-col space-y-4 p-4">
                  <span className="mb-2 font-semibold">Target</span>
                  <div className="flex">
                    <span className="flex-1">Insurance application</span>
                    <span
                      className="flex-[0.1] text-center"
                      dangerouslySetInnerHTML={{
                        __html: getTargetStatus(
                          driver?.relationship?.target?.insurance_application
                        ),
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
    </motion.div>
  );
};

export default DriverInformation;
