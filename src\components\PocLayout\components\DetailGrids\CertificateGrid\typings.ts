interface LienHolder {
  name: string;
  lien_date: string;
  address: {
    street: string;
    city: string;
    state: string;
    zip_code: string;
  };
}
interface Owner {
  names: string[];
  ownership_type: string;
  address: {
    street: string;
    city: string;
    state: string;
    zip_code: string;
  };
}
interface Details {
  title_no: string;
  vin: string;
  year: string;
  make: string;
  model: string;
  body_style: string;
  issue_date: string;
  title_type: string;
  license_plate: string;
  is_black_and_white: boolean;
  titled_state: string;
  odometer: {
    reading: string;
    brand: string;
  };
  lien_holder: LienHolder[];
  owner: Owner;
}
export interface CertificateGridProps {
  details: Details;
}
