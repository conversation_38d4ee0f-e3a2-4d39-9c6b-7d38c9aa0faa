version: "3.4"

services:
  app:

    build:
      context: .
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_S3_REGION=${NEXT_PUBLIC_S3_REGION}  
        - NEXT_PUBLIC_S3_SSE_CUSTOMER_KEY=${NEXT_PUBLIC_S3_SSE_CUSTOMER_KEY}
        - NEXT_PUBLIC_AWS_PROJECT_REGION=${NEXT_PUBLIC_AWS_PROJECT_REGION}
        - NEXT_PUBLIC_AWS_COGNITO_REGION=${NEXT_PUBLIC_AWS_COGNITO_REGION}
        - NEXT_PUBLIC_AWS_COGNITO_IDENTITY_POOL_ID=${NEXT_PUBLIC_AWS_COGNITO_IDENTITY_POOL_ID}
        - NEXT_PUBLIC_AWS_USER_POOLS_ID=${NEXT_PUBLIC_AWS_USER_POOLS_ID}
        - NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID=${NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID}

    volumes:
      - "/mnt/user-data:/mnt/user-data:rw"
      
    ports:
      - "3000:3000"

    env_file:
      - .env