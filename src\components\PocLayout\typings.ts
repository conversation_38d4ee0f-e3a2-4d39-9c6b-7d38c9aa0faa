export interface PocLayoutProps {
  slug?: string;
  subtitle?: string;
  keyword?: string;
}

export interface InsurancePaths {
  driving_license: string;
  itc: string;
  crm_receipt: string;
  application: string;
  motor_vehicle_record: string;
  broker_package: string;
  pleasure_use_letter?: string | any;
  artisan_use_letter?: string | any;
  eft?: string | any;
  non_owners_letter?: string | any;
  stripe_receipt?: string | any;
  registration?: string | any;
  vehicle_record?: string | any;
  promise_to_provide?: string | any;
}

export interface DDocument {
  id: string;
  title: string;
  type: string;
}

export interface DocumentState extends DDocument {
  file: File;
}
