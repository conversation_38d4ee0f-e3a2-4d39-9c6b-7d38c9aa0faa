import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import formidable, { File as FormidableFile } from "formidable";
import fs from "fs";
import { s3Values } from "@constants/s3";
import { getBucketName } from "@utils";
import { NextApiRequest, NextApiResponse } from "next";

interface RequestProps extends NextApiRequest {
  slug: string | any;
  formData: FormidableFile | any;
  userId: string | any;
  keys: string[] | any;
}

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: RequestProps, res: NextApiResponse) {
  if (req.method !== "POST") {
    res.status(405).json({ message: "Method not allowed" });
  }
  const form = formidable({
    multiples: true,
  });

  form.parse(req, async (err, fields, files) => {
    if (err) {
      res.status(500).json({ message: "Error parsing the form data" });
      return;
    }

    const slug = Array.isArray(fields.slug) ? fields.slug[0] : fields.slug;
    const { keys } = fields;
    const userId = Array.isArray(fields.userId)
      ? fields.userId[0]
      : fields.userId;

    if (
      !slug ||
      !userId ||
      !files ||
      !keys ||
      Object.values(files).length === 0
    ) {
      res.status(400).json({ message: "Missing required fields or files" });
      return;
    }
    const s3 = new S3Client({
      region: s3Values.region,
      endpoint: `https://s3.${s3Values.region}.amazonaws.com`,
    });

    const fileArray: any = [];

    Object.entries(files)?.forEach(([k, filesArr]) => {
      filesArr?.forEach((file) =>
        fileArray.push({
          originalFileName: file?.originalFilename,
          path: file?.filepath,
        })
      );
    });

    keys?.forEach((key) => {
      fileArray.map((p: any, index: number) => {
        if (
          p?.originalFileName?.toLowerCase() ===
          key?.split("request/")?.[1]?.toLowerCase()
        ) {
          fileArray[index]["key"] = key;
        }
      });
    });

    const uploadPromises = fileArray?.map(async ({ path, key }: any) => {
      const fileStream = fs.createReadStream(path);
      const params = {
        Bucket: getBucketName(slug),
        Key: key,
        Body: fileStream,
        SSECustomerAlgorithm: "AES256",
        SSECustomerKey: s3Values.sseCustomerKey,
      };
      const command = new PutObjectCommand(params);
      await s3.send(command);
    });

    try {
      await Promise.all(uploadPromises);
      res.status(200).json({ message: "File uploaded successfully" });
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Error uploading the file to S3" });
    }
  });
}
