import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import formidable, { File as FormidableFile } from "formidable";
import fs from "fs";
import { s3Values } from "@constants/s3";
import { getBucketName } from "@utils";
import { NextApiRequest, NextApiResponse } from "next";

interface RequestProps extends NextApiRequest {
  slug: string | any;
  formData: FormidableFile | any;
  userId: string | any;
}

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: RequestProps, res: NextApiResponse) {
  if (req.method !== "POST") {
    res.status(405).json({ message: "Method not allowed" });
  }
  const form = formidable({
    multiples: false,
  });

  form.parse(req, async (err, fields, files) => {
    if (err) {
      res.status(500).json({ message: "Error parsing the form data" });
      return;
    }

    const slug = Array.isArray(fields.slug) ? fields.slug[0] : fields.slug;
    const userId = Array.isArray(fields.userId)
      ? fields.userId[0]
      : fields.userId;
    const fileArray = files.file as FormidableFile[] | FormidableFile;
    const formData = Array.isArray(fileArray) ? fileArray[0] : fileArray;

    if (!slug || !userId || !formData) {
      res.status(400).json({ message: "Missing required fields" });
      return;
    }
    const bucketName = getBucketName(slug as string);

    const s3 = new S3Client({
      region: s3Values.region,
      endpoint: `https://s3.${s3Values.region}.amazonaws.com`,
    });

    try {
      const fileStream = fs.createReadStream(formData.filepath);
      const params = {
        Bucket: bucketName,
        Key: `user-data/${userId}/${formData.originalFilename}`,
        Body: fileStream,
        SSECustomerAlgorithm: "AES256",
        SSECustomerKey: s3Values.sseCustomerKey,
      };
      const command = new PutObjectCommand(params);
      await s3.send(command);
      res.status(200).json({ message: "File uploaded successfully" });
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Error uploading the file to S3" });
    }
  });
}
