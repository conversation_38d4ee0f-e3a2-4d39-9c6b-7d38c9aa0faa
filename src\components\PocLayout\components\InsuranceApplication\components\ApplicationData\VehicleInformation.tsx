import React from "react";
import { motion } from "framer-motion";
import { getTargetStatus } from "@utils";
import cx from "classnames";

interface VehicleInformationProps {
  info: any;
}

const VehicleInformation: React.FC<VehicleInformationProps> = ({ info }) => {
  return (
    <motion.div
      className="flex flex-col shadow-md rounded-lg"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <div className="flex items-center space-x-4 bg-gray-100 p-4 rounded-tl-lg rounded-tr-lg font-semibold">
        <div className="flex-1">ID</div>
        <div className="flex-1">Make</div>
        <div className="flex-1">Model</div>
        <div className="flex-1">VIN</div>
        <div className="flex-1">Year</div>
        <div className="flex-1">Is valid</div>
      </div>
      {info?.vehicles?.map(
        ({ id, make, model, vin, year, is_valid }: any, index: number) => (
          <div
            className={cx("flex items-center space-x-4 p-4 border border-t-0", {
              "bg-gray-100": index % 2 !== 0,
              "border-b-0": index !== info?.vehicles?.length - 1,
              "rounded-br-lg rounded-bl-lg":
                index === info?.vehicles?.length - 1,
            })}
            key={index}
          >
            <div className="flex-1">{id || "-"}</div>
            <div className="flex-1">{make || "-"}</div>
            <div className="flex-1">{model || "-"}</div>
            <div className="flex-1">{vin || "-"}</div>
            <div className="flex-1">{year || "-"}</div>
            <div
              className="flex-1"
              dangerouslySetInnerHTML={{ __html: getTargetStatus(is_valid) }}
            />
          </div>
        )
      )}
    </motion.div>
  );
};

export default VehicleInformation;
