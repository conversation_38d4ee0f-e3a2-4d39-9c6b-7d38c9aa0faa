import React, { useState } from "react";
import { motion } from "framer-motion";
import { Raleway } from "next/font/google";
import { Sample } from "@typings";
import Image from "next/image";
import { Download, Upload, Uploaded } from "@public/assets/icons";
import { docIds, uploadDocuments } from "./documents";
import _ from "lodash";
import { imageTypes, pdfType } from "@utils";
import { InsuranceApplicationProps } from "./typings";
import { DDocument } from "../../typings";
import { ApplicationData } from "./components";
import cx from "classnames";

const raleway = Raleway({
  display: "swap",
  weight: ["400", "500", "600"],
  subsets: ["latin"],
});

const InsuranceApplication: React.FC<InsuranceApplicationProps> = ({
  insuranceDetails,
  subtitle,
  isUploadResult,
  samples,
  documentHasError,
  selectInsuranceFile,
  handleInsuranceFileDownload,
  resetInsuranceDocument,
  handleSelectedInsuranceFile,
  hasAllRequiredInsuranceFiles,
  getInsuranceDetails,
  insuranceFileUploadIds,
}) => {
  const { required: requiredDocs, optional: optionalDocs } = uploadDocuments;
  const [isHovered, setIsHovered] = useState<boolean>(false);
  const [isActive, setIsActive] = useState<boolean>(false);
  return (
    <motion.div
      className="flex flex-col space-y-16 mx-auto p-16 transform duration-200 lg:translate-y-32"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <motion.div className="flex flex-col space-y-4 items-center text-brand-black">
        <h2 className="text-4xl font-semibold text-center">Try now!</h2>
        <p
          className={`${raleway.className} max-w-xl text-center font-medium leading-relaxed`}
        >
          {subtitle}
        </p>
      </motion.div>
      <motion.div
        className="bg-brand-white mx-auto max-w-5xl p-6 shadow-md rounded-lg"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <div className="bg-white p-6 rounded-xl shadow-md">
          <div
            className={`${raleway.className} flex gap-5 leading-5 text-center font-medium flex-wrap`}
          >
            {samples?.map((sample: Sample, index: number) => (
              <motion.div
                className={cx(
                  "relative pb-28 flex flex-1 flex-col space-y-4 items-center rounded-lg bshadow p-3 transition duration-300 cursor-default",
                  {
                    "bshadow-secondary-light": isActive,
                  }
                )}
                key={sample?.folderName}
                initial={{ scale: 0 }}
                onClick={() => setIsActive(true)}
                animate={{ scale: 1 }}
                transition={{
                  scale: { duration: 0.3, delay: index / 10 },
                }}
                whileHover={
                  isHovered || isActive ? {} : { backgroundColor: "#F0544310" }
                }
              >
                <span className="absolute top-0 left-0 bg-brand-secondary/80 rounded-tl-lg rounded-br-lg px-2 text-[10px] before:absolute before:h-[20px] before:w-[22px] text-white before:bg-white before:blur-[6px] before:top-0 before:left-[21px] before:skew-x-[-25deg] before:opacity-0 latest-animation before:z-40 overflow-hidden">
                  Sample
                </span>
                {isActive && (
                  <span className="absolute -top-5 -right-1">
                    <i className="fas fa-circle-check text-brand-secondary/60"></i>
                  </span>
                )}
                <span
                  className="h-[40px] line-clamp-2 whitespace-nowrap"
                  title={sample?.title}
                >
                  {sample?.title}
                </span>

                <motion.span
                  initial={{ scale: 1 }}
                  transition={{ scale: { duration: 0.2 } }}
                  whileTap={{ scale: 0.9 }}
                  className="relative focus:outline-none min-w-[70px] left-[-50px]"
                >
                  {sample?.paths?.map((image: string, index: number) => (
                    <Image
                      src={image}
                      className="absolute w-[200px] h-[111px] flex justify-center items-center rounded-lg cursor-pointer bshadow bg-white object-contain focus:outline-none -top-4"
                      style={{
                        left: `${index * 20}px`,
                      }}
                      width={110}
                      height={111}
                      alt={sample?.title!}
                      priority
                      onClick={() => selectInsuranceFile(sample)}
                      key={image}
                    />
                  ))}
                </motion.span>
                <motion.button
                  className="absolute bottom-2 right-2 z-10 cursor-pointer"
                  onClick={() => {
                    handleInsuranceFileDownload(sample?.folderName);
                  }}
                  onMouseOver={() => setIsHovered(true)}
                  onMouseOut={() => setIsHovered(false)}
                  whileHover={{
                    scale: 0.9,
                  }}
                >
                  <Download />
                </motion.button>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>
      <motion.div
        className="relative bg-brand-white mx-auto max-w-5xl mb-12 shadow-md rounded-lg"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <div className="flex flex-col gap-4 p-6 rounded-lg">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex flex-col space-y-4 flex-1 p-6 bg-white rounded-xl shadow-md">
              <span className="absolute top-0 left-0 p-3 bg-brand-secondary/10 rounded-tl-xl rounded-br-xl font-semibold text-brand-secondary/75">
                Required
              </span>
              <div className="grid grid-cols-2 gap-4 items-center translate-y-10 pb-10">
                {requiredDocs?.map((document: DDocument, index: number) => {
                  const { id, title } = document;
                  return (
                    <motion.form
                      className="relative flex flex-col space-y-4 items-center rounded-lg bshadow p-3"
                      encType="multipart/form-data"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{
                        duration: 0.4,
                        delay: index / requiredDocs.length,
                      }}
                      key={id}
                    >
                      <span className="whitespace-nowrap text-[12px] md:text-[14px]">
                        {title}
                      </span>

                      <motion.label
                        className="relative flex justify-center items-center rounded-lg w-[90px] md:w-[110px] h-[70px] lg:h-[120px] bg-white bshadow focus:outline-none"
                        htmlFor={id}
                        initial={{ scale: 1 }}
                        transition={{ duration: 0.2 }}
                        whileTap={
                          !insuranceFileUploadIds.includes(id)
                            ? { scale: 0.9 }
                            : {}
                        }
                        whileHover={
                          !insuranceFileUploadIds.includes(id)
                            ? { cursor: "pointer" }
                            : {}
                        }
                      >
                        {insuranceFileUploadIds.includes(id) &&
                        !documentHasError ? (
                          <>
                            <span
                              className="flex items-center justify-center absolute -top-2 -right-2 text-white text-[14px] bg-brand-secondary/60 rounded-full px-1.5 cursor-pointer z-50"
                              onClick={(event: React.MouseEvent) =>
                                resetInsuranceDocument(event, id)
                              }
                            >
                              &times;
                            </span>
                            <div className="flex flex-col space-y-2 text-[12px] font-semibold text-brand-secondary/70">
                              <motion.span
                                className="upload-icon"
                                initial={{
                                  opacity: 0,
                                  display: "none",
                                  rotate: 90,
                                }}
                                animate={{
                                  opacity: 1,
                                  display: "block",
                                  rotate: 0,
                                }}
                                transition={{ duration: 0.3, delay: 0.5 }}
                              >
                                <Uploaded />
                              </motion.span>
                              <motion.span
                                className="text-[12px] md:text-[14px] font-semibold text-brand-secondary/70"
                                initial={{
                                  scale: 0.8,
                                  opacity: 1,
                                  display: "block",
                                }}
                                animate={{
                                  scale: 1,
                                  opacity: 0,
                                  display: "none",
                                }}
                                transition={{ duration: 0.5, delay: 0.5 }}
                                exit={{ opacity: 0 }}
                              >
                                Uploaded!
                              </motion.span>
                            </div>
                          </>
                        ) : (
                          <>
                            <input
                              type="file"
                              id={id}
                              className="hidden"
                              onChange={(event) =>
                                handleSelectedInsuranceFile(event, document)
                              }
                              accept={
                                document.id === docIds.license
                                  ? imageTypes
                                  : pdfType
                              }
                            />
                            <span>
                              <Upload />
                            </span>
                          </>
                        )}
                      </motion.label>
                    </motion.form>
                  );
                })}
              </div>
            </div>
            <div className="relative flex flex-col space-y-4 flex-1 bg-white p-6 rounded-xl shadow-md">
              <span className="absolute top-0 left-0 p-3 bg-brand-gray rounded-tl-xl rounded-br-xl font-semibold text-brand-black/75">
                Optional
              </span>
              <div className="grid grid-cols-2 gap-4 translate-y-10 pb-10">
                {optionalDocs?.map((document: DDocument, index: number) => {
                  const { id, title } = document;
                  return (
                    <motion.form
                      className="relative flex flex-col space-y-4 items-center rounded-lg bshadow p-3"
                      encType="multipart/form-data"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{
                        duration: 0.4,
                        delay: index / optionalDocs.length,
                      }}
                      key={id}
                    >
                      <span className="whitespace-nowrap text-[12px] md:text-[14px]">
                        {title}
                      </span>
                      <motion.label
                        className="relative flex justify-center items-center rounded-lg w-[90px] md:w-[110px] h-[70px] bg-white bshadow focus:outline-none"
                        htmlFor={id}
                        initial={{ scale: 1 }}
                        transition={{ duration: 0.2 }}
                        whileTap={
                          !insuranceFileUploadIds.includes(id)
                            ? { scale: 0.9 }
                            : {}
                        }
                        whileHover={
                          !insuranceFileUploadIds.includes(id)
                            ? { cursor: "pointer" }
                            : {}
                        }
                      >
                        {insuranceFileUploadIds.includes(id) &&
                        !documentHasError ? (
                          <>
                            <span
                              className="flex items-center justify-center absolute -top-2 -right-2 text-white text-[14px] bg-brand-secondary/60 rounded-full px-1.5 cursor-pointer z-50"
                              onClick={(event: React.MouseEvent) =>
                                resetInsuranceDocument(event, id)
                              }
                            >
                              &times;
                            </span>
                            <div className="flex flex-col space-y-2 text-[12px] font-semibold text-brand-secondary/70">
                              <motion.span
                                className="upload-icon"
                                initial={{
                                  opacity: 0,
                                  display: "none",
                                  rotate: 90,
                                }}
                                animate={{
                                  opacity: 1,
                                  display: "block",
                                  rotate: 0,
                                }}
                                transition={{ duration: 0.3, delay: 0.6 }}
                              >
                                <Uploaded />
                              </motion.span>
                              <motion.span
                                className="text-[12px] md:text-[14px] font-semibold text-brand-secondary/70"
                                initial={{
                                  scale: 0.8,
                                  opacity: 1,
                                  display: "block",
                                }}
                                animate={{
                                  scale: 1,
                                  opacity: 0,
                                  display: "none",
                                }}
                                transition={{ duration: 0.5, delay: 0.5 }}
                                exit={{ opacity: 0 }}
                              >
                                Uploaded!
                              </motion.span>
                            </div>
                          </>
                        ) : (
                          <>
                            <input
                              type="file"
                              id={id}
                              className="hidden"
                              onChange={(event) =>
                                handleSelectedInsuranceFile(event, document)
                              }
                              accept={
                                document.id === docIds.registration
                                  ? imageTypes
                                  : pdfType
                              }
                            />
                            <span>
                              <Upload />
                            </span>
                          </>
                        )}
                      </motion.label>
                    </motion.form>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
        <button
          className="w-full p-4 bg-brand-secondary/70 text-white btn-disabled font-semibold tracking-wide hover:bg-brand-secondary/60 transition duration-200 rounded-bl-lg rounded-br-lg"
          disabled={!hasAllRequiredInsuranceFiles}
          onClick={getInsuranceDetails}
        >
          Start Analysis
        </button>
      </motion.div>
      {isUploadResult && (
        <ApplicationData data={insuranceDetails} hasResult={isUploadResult} />
      )}
    </motion.div>
  );
};

export default InsuranceApplication;
