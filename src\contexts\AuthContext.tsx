import React, { createContext, useContext, useState } from "react";
import { useRouter } from "next/router";
import {
  signIn,
  signOut,
  getCurrentUser,
  fetchUserAttributes,
  fetchAuthSession,
  confirmSignIn,
} from "aws-amplify/auth";
import { slugs } from "@constants/slugs";
import { DEFAULT_PASSWORD, NEW_PASSWORD_STEP } from "@constants";

interface AuthContextProps {
  user: any;
  roles: string[];
  loading: boolean;
  error: string;
  setError: React.Dispatch<React.SetStateAction<string>>;
  loginUser: (username: string, password: string) => Promise<void>;
  logoutUser: () => Promise<void>;
  checkUserSession: () => Promise<void>;
}

const AuthContext = createContext<AuthContextProps | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const [roles, setRoles] = useState<string[]>([]);
  const router = useRouter();

  const logError = (context: string, error: any) => {
    console.error(`${context} - Error:`, error?.message || error);
    if (error?.code) {
      console.error(`${context} - Error Code:`, error.code);
    }
  };

  const fetchUser = async () => {
    try {
      setLoading(true);
      const userData = await getCurrentUser();
      const attributes = await fetchUserAttributes();
      setUser(userData);

      if (attributes) {
        const userRoles = Object.keys(attributes)
          ?.filter(
            (key) => key?.startsWith("custom:") && attributes?.[key] === "true"
          )
          ?.map((key) => key?.split(":")?.[1]);
        setRoles(userRoles);
        if (typeof window !== "undefined") {
          localStorage.setItem("userId", attributes.sub!);
        }
      }
    } catch (error) {
      logError("Fetching User", error);
    } finally {
      setLoading(false);
    }
  };

  const checkUserSession = async () => {
    try {
      setLoading(true);
      await fetchAuthSession();
      await fetchUser();
    } catch (err) {
      logError("User Session Check", error);
      setUser(null);
      if (router.asPath.replace("/", "") !== slugs.login) {
        router.replace(`/${slugs.login}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const loginUser = async (username: string, password: string) => {
    try {
      const { isSignedIn, nextStep } = await signIn({ username, password });

      //For signin confirmation and default password set of newly created user at the aws console
      if (nextStep.signInStep === NEW_PASSWORD_STEP) {
        await confirmSignIn({
          challengeResponse: DEFAULT_PASSWORD,
        });
      }

      if (isSignedIn) {
        await fetchUser();
        router.replace(slugs.home);
      }
    } catch (error) {
      logError("Login", error);
      setError("Oops, something went wrong!");
    }
  };

  const logoutUser = async () => {
    try {
      setLoading(true);
      await signOut();
      setUser(null);
      setRoles([]);
      router.replace(`/${slugs.login}`);
      if (typeof window !== "undefined") {
        localStorage.clear();
        sessionStorage.clear();
      }
    } catch (error) {
      logError("Logout", error);
      setError("Oops, something went wrong!");
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        roles,
        loading,
        error,
        setError,
        loginUser,
        logoutUser,
        checkUserSession,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuthState = (): AuthContextProps => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuthState must be used within an AuthProvider");
  }
  return context;
};
