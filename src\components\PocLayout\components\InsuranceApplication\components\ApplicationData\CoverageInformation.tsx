import React from "react";
import { motion } from "framer-motion";
import { convertToSentenceCase, getTargetStatus } from "@utils";
import { Arrow } from "@public/assets/icons";

interface CoverageInformationProps {
  info: any;
  currentVehicleIndex: number;
  handlePreviousIndex: (event: React.MouseEvent<HTMLButtonElement>) => void;
  handleNextIndex: (event: React.MouseEvent<HTMLButtonElement>) => void;
}

const CoverageInformation: React.FC<CoverageInformationProps> = ({
  info,
  currentVehicleIndex,
  handlePreviousIndex,
  handleNextIndex,
}) => {
  return (
    <motion.div
      className="flex-1 flex flex-col space-y-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <div className="shadow-md rounded-lg">
        <div className="bg-gray-100 p-4 rounded-tr-lg rounded-tl-lg">
          Source:{" "}
          <span className="font-semibold">
            {convertToSentenceCase(info?.bodily_injury?.source) || "-"}
          </span>
        </div>
        <div className="flex p-2 border border-t-0 border-gray-200">
          <div className="flex-[0.3] font-semibold p-4">Bodily injury</div>
          <div className="flex-1 flex flex-col space-y-4 p-4">
            <span className="mb-2 font-semibold">Target</span>
            <div className="flex">
              <span className="flex-1">Insurance application</span>
              <span
                className="flex-[0.1] text-center"
                dangerouslySetInnerHTML={{
                  __html: getTargetStatus(
                    info?.bodily_injury?.target?.insurance_application
                  ),
                }}
              />
            </div>
          </div>
        </div>
        <div className="flex p-2 border border-t-0 border-gray-200 rounded-bl-lg rounded-br-lg">
          <div className="flex-[0.3] font-semibold p-4">Property damage</div>
          <div className="flex-1 flex flex-col space-y-4 p-4">
            <span className="mb-2 font-semibold">Target</span>
            <div className="flex">
              <span className="flex-1">Insurance application</span>
              <span
                className="flex-[0.1] text-center"
                dangerouslySetInnerHTML={{
                  __html: getTargetStatus(
                    info?.property_damage?.target?.insurance_application
                  ),
                }}
              />
            </div>
          </div>
        </div>
      </div>
      <div className="flex p-4 items-center justify-between border rounded-lg shadow-md">
        <span>Uninsured bodily injury</span>
        <span>{info?.uninsured_bodily_injury || "-"}</span>
      </div>
      <div className="flex p-4 items-center justify-between border rounded-lg shadow-md">
        <span>Uninsured property damage</span>
        <span>{info?.uninsured_property_damage || "-"}</span>
      </div>
      <section>
        <div className="w-full bg-brand-secondary/10 text-brand-secondary/75 font-semibold text-lg rounded-tl-lg rounded-tr-lg px-4 py-3">
          Vehicles
        </div>
        {info?.vehicles
          ?.slice(currentVehicleIndex, currentVehicleIndex + 1)
          ?.map((vehicle: any, index: number) => (
            <div
              className="relative flex flex-col space-y-4 border border-t-0 p-4 rounded-br-lg rounded-bl-lg"
              key={index}
            >
              <div className="flex items-center justify-between border p-4 rounded-lg shadow-md">
                <span>ID</span>
                <span>{vehicle?.id || "-"}</span>
              </div>
              <div className="shadow-md rounded-lg">
                <div className="bg-gray-100 p-4 rounded-tr-lg rounded-tl-lg">
                  Source:{" "}
                  <span className="font-semibold">
                    {convertToSentenceCase(
                      vehicle?.comprehensive_deductible?.source
                    ) || "-"}
                  </span>
                </div>
                <div className="flex p-2 border border-t-0 border-gray-100 rounded-bl-lg rounded-br-lg">
                  <div className="flex-[0.3] font-semibold p-4">
                    Comprehensive deductible
                  </div>
                  <div className="flex-1 flex flex-col space-y-4 p-4">
                    <span className="mb-2 font-semibold">Target</span>
                    <div className="flex">
                      <span className="flex-1">Insurance application</span>
                      <span
                        className="flex-[0.1] text-center"
                        dangerouslySetInnerHTML={{
                          __html: getTargetStatus(
                            vehicle?.comprehensive_deductible?.target
                              ?.insurance_application
                          ),
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="shadow-md rounded-lg">
                <div className="bg-gray-100 p-4 rounded-tr-lg rounded-tl-lg">
                  Source:{" "}
                  <span className="font-semibold">
                    {convertToSentenceCase(
                      vehicle?.collision_deductible?.source
                    ) || "-"}
                  </span>
                </div>
                <div className="flex p-2 border border-t-0 border-gray-100 rounded-bl-lg rounded-br-lg">
                  <div className="flex-[0.3] font-semibold p-4">
                    Collision deductible
                  </div>
                  <div className="flex-1 flex flex-col space-y-4 p-4">
                    <span className="mb-2 font-semibold">Target</span>
                    <div className="flex">
                      <span className="flex-1">Insurance application</span>
                      <span
                        className="flex-[0.1] text-center"
                        dangerouslySetInnerHTML={{
                          __html: getTargetStatus(
                            vehicle?.collision_deductible?.target
                              ?.insurance_application
                          ),
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
              {info?.vehicles?.length > 1 && (
                <>
                  <motion.button
                    className="flex items-center justify-center w-10 text-white h-10 bg-brand-secondary/70 rounded-full absolute z-[999] top-48 -left-7 navigation-left focus:outline-none border-0 disabled:bg-gray-200 pointer-events-auto"
                    onClick={handlePreviousIndex}
                    initial={{ opacity: 0, scale: 1 }}
                    animate={{ opacity: 1 }}
                    whileTap={currentVehicleIndex !== 0 ? { scale: 0.8 } : {}}
                    transition={{ duration: 0.2 }}
                    disabled={currentVehicleIndex === 0}
                  >
                    <Arrow />
                  </motion.button>
                  <motion.button
                    className="flex items-center justify-center w-10 h-10 text-white bg-brand-secondary/70 rounded-full absolute z-[999] top-48 -right-7 -translate-y-1/2 navigation-right focus:outline-none border-0 disabled:bg-gray-200 pointer-events-auto"
                    onClick={handleNextIndex}
                    initial={{ opacity: 0, scale: 1 }}
                    animate={{ opacity: 1 }}
                    whileTap={
                      currentVehicleIndex === info?.vehicles?.length - 1
                        ? { scale: 0.8 }
                        : {}
                    }
                    transition={{ duration: 0.2 }}
                    disabled={
                      currentVehicleIndex === info?.vehicles?.length - 1
                    }
                  >
                    <Arrow />
                  </motion.button>
                </>
              )}
            </div>
          ))}
      </section>
    </motion.div>
  );
};

export default CoverageInformation;
