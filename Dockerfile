FROM node:21-alpine

WORKDIR /app

ARG NEXT_PUBLIC_S3_REGION
ARG NEXT_PUBLIC_S3_SSE_CUSTOMER_KEY
ARG NEXT_PUBLIC_AWS_PROJECT_REGION
ARG NEXT_PUBLIC_AWS_COGNITO_REGION
ARG NEXT_PUBLIC_AWS_COGNITO_IDENTITY_POOL_ID
ARG NEXT_PUBLIC_AWS_USER_POOLS_ID
ARG NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID

ENV NEXT_PUBLIC_S3_REGION ${NEXT_PUBLIC_S3_REGION}  
ENV NEXT_PUBLIC_S3_SSE_CUSTOMER_KEY ${NEXT_PUBLIC_S3_SSE_CUSTOMER_KEY}
ENV NEXT_PUBLIC_AWS_PROJECT_REGION ${NEXT_PUBLIC_AWS_PROJECT_REGION}
ENV NEXT_PUBLIC_AWS_COGNITO_REGION ${NEXT_PUBLIC_AWS_COGNITO_REGION}
ENV NEXT_PUBLIC_AWS_COGNITO_IDENTITY_POOL_ID ${NEXT_PUBLIC_AWS_COGNITO_IDENTITY_POOL_ID}
ENV NEXT_PUBLIC_AWS_USER_POOLS_ID ${NEXT_PUBLIC_AWS_USER_POOLS_ID}
ENV NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID ${NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID}

RUN apk add --no-cache yarn

COPY package*.json ./
COPY yarn.lock    ./

RUN yarn install \
  --no-progress \
  --non-interactive \
  --frozen-lockfile

COPY . ./

RUN yarn build

EXPOSE 3000

CMD ["yarn", "start"]