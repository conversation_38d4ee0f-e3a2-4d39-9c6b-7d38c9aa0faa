import Image from "next/image";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import _ from "lodash";
import Head from "next/head";
import { Loader } from "@components";
import { motion } from "framer-motion";
import { useAuthState } from "@contexts/AuthContext";

export default function Home() {
  const [pocs, setPocs] = useState<any>([]);
  const { user, roles } = useAuthState();

  useEffect(() => {
    async function getPocs() {
      try {
        const response = await fetch("/api/pocs", {
          headers: {
            Accept: "application/json",
            method: "GET",
          },
        });
        if (response.ok) {
          const pocData = await response.json();
          const filteredPocs = pocData?.data?.filter((poc: any) =>
            roles?.includes(poc?.name)
          );
          setPocs(filteredPocs);
        }
      } catch (error) {
        console.log(error);
      }
    }
    if (user) {
      getPocs();
    }
  }, []);
  return (
    <div className="container mx-auto px-8">
      <Head>
        <title>Data Science POC Solutions | Maruti Techlabs</title>
      </Head>
      {!user ? (
        <Loader />
      ) : (
        <motion.section
          className={`grid grid-cols-1 ${
            pocs?.length === 1 ? `lg:grid-cols-1` : `lg:grid-cols-2`
          } gap-10 mt-20 mb-32 relative top-32`}
          initial={{ y: 0 }}
          animate={{ y: -50 }}
          transition={{ duration: 0.9 }}
        >
          {pocs?.map(({ id, src, title, slug, subtitle }: any) => (
            <React.Fragment key={id}>
              <Link href={slug}>
                <motion.div
                  className="relative group transform duration-300 cursor-pointer shadow-5xl rounded-md bg-gray-50 overflow-hidden"
                  key={id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                >
                  <Image
                    src={src}
                    alt={src}
                    priority
                    width={565}
                    height={280}
                    className="min-w-full rounded-md hover:shadow-5xl shadow-black object-contain transition-all duration-300 group-hover:scale-110"
                  />

                  <div
                    className={`absolute flex flex-col space-y-4 md:space-y-2 md:group-hover:space-y-0 -bottom-8 group-hover:space-y-1 group-hover:bottom-0 transform duration-300 p-4 w-full bg-black bg-opacity-30`}
                  >
                    <span className="font-semibold text-white text-sm md:text-md lg:text-lg">
                      {title}
                    </span>
                    <span className="font-thin text-white text-xs md:text-sm lg:text-md line-clamp-1">
                      {subtitle}
                    </span>
                  </div>
                </motion.div>
              </Link>
            </React.Fragment>
          ))}
        </motion.section>
      )}
    </div>
  );
}
