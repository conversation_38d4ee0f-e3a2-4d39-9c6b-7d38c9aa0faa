import { Sample } from "@typings";
import { DDocument, DocumentState, InsurancePaths } from "../../typings";

export interface InsuranceApplicationProps {
  subtitle?: string;
  samples: Sample[];
  isUploadResult: boolean;
  insuranceDetails: InsuranceApplicationResponse;
  documentHasError: boolean;
  insuranceFileUploadIds: string[] | any;
  selectInsuranceFile: (sample: Sample) => void;
  handleInsuranceFileDownload: (folderName: string | any) => void;
  resetInsuranceDocument: (
    event: React.MouseEvent,
    id: string | any
  ) => Promise<void>;
  handleSelectedInsuranceFile: (
    event: React.ChangeEvent<HTMLInputElement>,
    document: DDocument
  ) => void;
  hasAllRequiredInsuranceFiles: boolean | any;
  getInsuranceDetails: (sample?: Sample | any) => Promise<void>;
}

export interface InsuranceApplicationResponse {
  insured_information: {
    name: {
      source: string;
      target: {
        insurance_application: boolean;
        crm_receipt: boolean;
        itc: boolean;
        pleasure_use_letter: any;
        broker_package: boolean;
        mvr: boolean;
        artisan_use_letter: any;
        non_owners_letter: any;
        eft: any;
        registration: any;
        vr: any;
      };
    };
    address: {
      source: string;
      target: {
        itc: boolean;
        insurance_application: boolean;
        crm_receipt: boolean;
      };
    };
    date_of_birth: {
      source: string;
      target: {
        insurance_application: boolean;
        itc: boolean;
        mvr: boolean;
      };
    };
    license_number: {
      source: string;
      target: {
        insurance_application: boolean;
        mvr: boolean;
      };
    };
  };
  policy_information: {
    policy_number: {
      source: string;
      target: {
        crm_receipt: boolean;
        pleasure_use_letter: any;
        artisan_use_letter: any;
        eft: any;
        non_owners_letter: any;
      };
    };
    policy_term: {
      source: string;
      target: {
        itc: boolean;
      };
    };
  };
  payment_information: {
    payment_date: any;
    down_payment: {
      source: string;
      target: {
        crm_receipt: boolean;
      };
    };
    vr_fee: any;
    broker_fee: {
      source: string;
      target: {
        broker_package: boolean;
      };
    };
    reference_number: any;
    amount_paid: any;
    payment_method: any;
  };
  line_of_business: {
    source: string;
    target: {
      crm_receipt: boolean;
    };
  };
  driver_information: {
    drivers: [
      {
        id: number;
        status: any;
        name: {
          source: string;
          target: {
            insurance_application: boolean;
            crm_receipt: boolean;
            itc: any;
            pleasure_use_letter: any;
            broker_package: boolean;
            mvr: boolean;
            artisan_use_letter: any;
            non_owners_letter: any;
            registration: any;
            vr: any;
          };
        };
        date_of_birth: {
          source: string;
          target: {
            itc: any;
            insurance_application: boolean;
            mvr: boolean;
          };
        };
        gender: {
          source: string;
          target: {
            insurance_application: boolean;
            itc: any;
            mvr: boolean;
          };
        };
        marital_status: any;
        driving_experience: any;
        sr_filing: any;
        license_number: {
          source: string;
          target: {
            insurance_application: boolean;
            mvr: boolean;
          };
        };
        number_of_violation: any;
        relationship: {
          source: string;
          target: {
            insurance_application: boolean;
          };
        };
      }
    ];
  };
  vehicle_information: {
    vehicles: [
      {
        id: number;
        make: any;
        model: any;
        vin: any;
        year: any;
        is_valid: any;
      }
    ];
  };
  coverage_information: {
    bodily_injury: {
      source: string;
      target: {
        insurance_application: boolean;
      };
    };
    property_damage: {
      source: string;
      target: {
        insurance_application: boolean;
      };
    };
    uninsured_bodily_injury: any;
    uninsured_property_damage: any;
    vehicles: [
      {
        id: number;
        comprehensive_deductible: {
          source: string;
          target: {
            insurance_application: boolean;
          };
        };
        collision_deductible: {
          source: string;
          target: {
            insurance_application: boolean;
          };
        };
      }
    ];
  };
  company_information: {
    name: {
      source: string;
      target: {
        pleasure_use_letter: any;
        non_owners_letter: any;
      };
    };
  };
  signature: {
    signature: {
      insurance_application: {
        third_party_designation: {
          is_signed: boolean;
          is_dated: boolean;
        };
        disclosure_of_all_household_members: {
          is_signed: boolean;
          is_dated: boolean;
        };
        rejection_of_bi_coverage: {
          is_signed: boolean;
          is_dated: boolean;
        };
        rejection_of_pd_coverage: {
          is_signed: boolean;
          is_dated: boolean;
        };
        vehicle_condition_certification: {
          is_signed: boolean;
          is_dated: boolean;
        };
        acknowledgements_by_applicant: {
          is_signed: boolean;
          is_dated: boolean;
        };
        acknowledgement_of_programs_offered: {
          is_signed: boolean;
          is_dated: boolean;
        };
        named_driver_exclusion: any;
        no_fault_accident_declaration: any;
        not_insurance_contract: {
          is_signed: boolean;
          is_dated: boolean;
        };
        consumer_disclosure: any;
        non_owned_vehicle_coverage_endorsement: any;
        cancellation_request: any;
      };
      itc: {
        insured_information: {
          is_signed: boolean;
        };
        vehicle_information: {
          is_signed: boolean;
        };
        eod_information: {
          is_signed: boolean;
        };
      };
      broker_package: {
        disclosures: {
          coverages: {
            is_signed: boolean;
          };
          disclosure_of_driving_record: {
            is_signed: boolean;
          };
          exclusion: {
            uninsured_bi_or_pd_coverage: {
              is_signed: boolean;
            };
            comprehensive_and_collision_coverage: {
              is_signed: boolean;
            };
            business_or_commercial_use: {
              is_signed: boolean;
            };
            named_drivers_limitation: {
              is_signed: boolean;
            };
          };
        };
        standard_broker_fee_disclosure: {
          is_signed: boolean;
        };
        text_messaging_consent_agreement: {
          is_signed: boolean;
        };
        broker_fee_agreement: {
          client_initials: {
            is_signed: boolean;
          };
          condition_and_acknowledgement_agreement: {
            is_signed: boolean;
          };
          client_signature: {
            is_signed: boolean;
          };
        };
      };
      artisan_use_letter: any;
      non_owners_letter: any;
      eft: any;
      pleasure_use_letter: any;
      promise_to_provide: any;
      mvr: {
        is_signed: boolean;
      };
    };
  };
  promise_to_provide_information: any;
}
