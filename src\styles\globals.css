@tailwind base;
@tailwind components;
@tailwind utilities;

@import "hover.css/css/hover-min.css";
@import "@components/styles.css";
@import "@pages/styles.css";

html {
  scroll-behavior: smooth;
}
.check-icon {
  @apply text-lime-600 text-xl;
}
.cross-icon {
  @apply text-brand-secondary/80 text-xl;
}
.custom-scroller {
  scrollbar-width: thin !important;
  scrollbar-color: #f5f5f5 #ffffff !important;
}
.custom-scroller::-webkit-scrollbar {
  width: 4px !important;
}
.custom-scroller::-webkit-scrollbar-track {
  background-color: transparent !important;
}
.custom-scroller::-webkit-scrollbar-thumb {
  border-radius: 6px !important;
  border: 3px solid transparent !important;
  background-color: lightgray !important;
}
.custom-scroller.no-scroller {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}
.custom-scroller.no-scroller::-webkit-scrollbar {
  -webkit-appearance: none !important;
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}
.suggestion {
  @apply flex-shrink-0 border border-brand-secondary/50 rounded-lg py-2 px-3 cursor-pointer hover:bg-brand-secondary/80 hover:text-white transition duration-150 disabled:cursor-default disabled:border-gray-400 disabled:hover:bg-transparent disabled:text-gray-400;
}
.qa-height {
  height: 725px !important;
}
.query-loader {
  width: 40px;
  aspect-ratio: 4;
  --_g: no-repeat radial-gradient(circle closest-side, #f05443 90%, #0000);
  background: var(--_g) 0% 50%, var(--_g) 50% 50%, var(--_g) 100% 50%;
  background-size: calc(100% / 3) 100%;
  animation: l7 1s infinite linear;
}
.navigation-left svg path,
.navigation-right svg path {
  fill: #f5f5f5;
}
.navigation-left:disabled svg path,
.navigation-right:disabled svg path {
  fill: gray;
}
.navigation-left {
  rotate: -90deg;
}
.navigation-right {
  rotate: 90deg;
}
@keyframes l7 {
  33% {
    background-size: calc(100% / 3) 0%, calc(100% / 3) 100%, calc(100% / 3) 100%;
  }
  50% {
    background-size: calc(100% / 3) 100%, calc(100% / 3) 0%, calc(100% / 3) 100%;
  }
  66% {
    background-size: calc(100% / 3) 100%, calc(100% / 3) 100%, calc(100% / 3) 0%;
  }
}
meter {
  border: none !important;
  outline: none !important;
}
meter::-webkit-meter-optimum-value {
  background: #f0544380;
}
meter::-web meter::-moz-meter-bar {
  background: #f0544380;
}
.latest-animation:before {
  animation: highlight 1s linear infinite;
}
@keyframes highlight {
  0% {
    opacity: 0;
    left: -15px;
  }
  20% {
    opacity: 0.3;
    left: 0;
  }
  30% {
    opacity: 0.45;
    left: 5px;
  }
  50% {
    opacity: 0.45;
    left: 10px;
  }
  80% {
    opacity: 0.45;
    left: 20px;
  }
  100% {
    opacity: 0;
    left: 35px;
  }
}

.btn-disabled {
  @apply disabled:cursor-default disabled:border-gray-400 disabled:bg-gray-200 disabled:text-gray-400 disabled:hover:bg-gray-200 disabled:pointer-events-none;
}
.upload-icon svg {
  fill: #f0544390;
}

.c-scroll::-webkit-scrollbar {
  width: 9px !important;
  height: 10px !important;
  transition: all 0.3s ease;
  border-radius: 8px !important;
}

.c-scroll::-webkit-scrollbar:hover {
  background: #efefef;
}

.c-scroll:hover::-webkit-scrollbar {
  transition: all 0.3s ease;
}

.c-scroll::-webkit-scrollbar-track {
  background-color: transparent !important;
}
.c-scroll::-webkit-scrollbar-thumb {
  border-radius: 8px !important;
  border: 2px solid white !important;
  background-color: #cacaca !important;
}
