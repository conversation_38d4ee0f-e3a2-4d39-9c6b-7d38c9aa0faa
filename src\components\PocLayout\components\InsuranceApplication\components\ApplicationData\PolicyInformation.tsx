import React from "react";
import { motion } from "framer-motion";
import {
  convertToSentenceCase,
  getTargetStatus,
  isValidTargetObject,
} from "@utils";
import cx from "classnames";

interface PolicyInformationProps {
  info: any;
}

const PolicyInformation: React.FC<PolicyInformationProps> = ({ info }) => {
  return (
    <motion.div
      className="flex-1 flex flex-col shadow-md rounded-lg"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <div className="bg-gray-100 p-4 rounded-tr-lg rounded-tl-lg">
        Source:{" "}
        <span className="font-semibold">
          {convertToSentenceCase(info?.policy_number?.source) || "-"}
        </span>
      </div>
      {info &&
        Object.keys(info).length > 0 &&
        Object.entries(info)?.map(([key, value]: any, index: number) => (
          <div
            className={cx("flex p-2 border border-gray-100", {
              "border-t-0 border-b-0": index === 0,
            })}
            key={key}
          >
            <div className="flex-[0.3] font-semibold p-4">
              {convertToSentenceCase(key)}
            </div>
            <div className="flex-1 flex flex-col space-y-4 p-4">
              <span className="mb-2 font-semibold">Target</span>
              {isValidTargetObject(value) &&
                Object.entries(value.target)?.map(([subKey, subValue]: any) => (
                  <div className="flex" key={subKey}>
                    <span className="flex-1">
                      {convertToSentenceCase(subKey)}
                    </span>
                    <span
                      className="flex-[0.1] text-center"
                      dangerouslySetInnerHTML={{
                        __html: getTargetStatus(subValue),
                      }}
                    />
                  </div>
                ))}
            </div>
          </div>
        ))}
    </motion.div>
  );
};

export default PolicyInformation;
