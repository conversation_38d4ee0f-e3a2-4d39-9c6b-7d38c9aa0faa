import { Sample } from "@typings";

export interface UploadGridProps {
  fileName: string;
  handleFile: (event: React.ChangeEvent<HTMLInputElement>) => void;
  samples: Sample[];
  isAMD: boolean;
  sampleIndex: number | any;
  isDragging: boolean;
  handleDragging: (dragging: boolean) => void;
  imageFile: string | any;
  imageFiles: string[] | any;
  handleDragOver: (
    event: React.DragEvent<HTMLDivElement | HTMLLabelElement>
  ) => void;
  handleDrop: (
    event: React.DragEvent<HTMLDivElement | HTMLLabelElement>
  ) => void;
  dragTimeout: React.RefObject<HTMLDivElement>;
  areMultipleFilesAllowed: boolean;
  currentObjectIndex: number;
  fileInputRef: React.RefObject<HTMLInputElement>;
  handleCurrentObjectIndex: React.Dispatch<React.SetStateAction<number>>;
}
