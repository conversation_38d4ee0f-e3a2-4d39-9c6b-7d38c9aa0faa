import React from "react";
import { motion } from "framer-motion";
import { convertToSentenceCase, getTargetStatus } from "@utils";

interface CompanyInformationProps {
  info: any;
}

const CompanyInformation: React.FC<CompanyInformationProps> = ({ info }) => {
  return (
    <motion.div
      className="shadow-md rounded-lg"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <div className="bg-gray-100 p-4 rounded-tr-lg rounded-tl-lg">
        Source:{" "}
        <span className="font-semibold">
          {convertToSentenceCase(info?.name?.source) || "-"}
        </span>
      </div>
      <div className="flex p-2 border border-t-0 border-gray-100 rounded-bl-lg rounded-br-lg">
        <div className="flex-[0.3] font-semibold p-4">Name</div>
        <div className="flex-1 flex flex-col space-y-4 p-4">
          <span className="mb-2 font-semibold">Target</span>
          <div className="flex">
            <span className="flex-1">Pleasure use letter</span>
            <span
              className="flex-[0.1] text-center"
              dangerouslySetInnerHTML={{
                __html: getTargetStatus(
                  info?.name?.target?.pleasure_use_letter
                ),
              }}
            />
          </div>
          <div className="flex">
            <span className="flex-1">Non owners letter</span>
            <span
              className="flex-[0.1] text-center"
              dangerouslySetInnerHTML={{
                __html: getTargetStatus(info?.name?.target?.non_owners_letter),
              }}
            />
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default CompanyInformation;
