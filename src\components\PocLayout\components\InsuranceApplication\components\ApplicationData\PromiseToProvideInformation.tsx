import React from "react";
import { motion } from "framer-motion";
import { convertToSentenceCase, getTargetStatus } from "@utils";
interface PromiseToProvideInformationProps {
  info: any;
}

const PromiseToProvideInformation: React.FC<
  PromiseToProvideInformationProps
> = ({ info }) => {
  const isPropertyNull = info === null || info === undefined;
  return (
    <motion.div
      className="relative flex flex-col space-y-2"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      {isPropertyNull ? (
        <div className="flex p-4 items-center justify-between border rounded-lg shadow-md mb-8">
          <div className="font-semibold">Promise to provide information</div>
          <span>-</span>
        </div>
      ) : (
        <div className="shadow-md rounded-lg">
          <div className="bg-gray-100 p-4 rounded-tr-lg rounded-tl-lg">
            Source:{" "}
            <span className="font-semibold">
              {convertToSentenceCase(info?.agreed_to_pay_date?.source) || "-"}
            </span>
          </div>
          <div className="flex p-2 border border-t-0 border-gray-100 rounded-bl-lg rounded-br-lg">
            <div className="flex-[0.3] font-semibold p-4">
              Agreed to pay date
            </div>
            <div className="flex-1 flex flex-col space-y-4 p-4">
              <span className="mb-2 font-semibold">Target</span>
              <div className="flex">
                <span className="flex-1">Promise to provide</span>
                <span
                  className="flex-[0.1] text-center"
                  dangerouslySetInnerHTML={{
                    __html: getTargetStatus(
                      info?.agreed_to_pay_date?.target?.promise_to_provide
                    ),
                  }}
                />
              </div>
            </div>
          </div>
          <div className="flex p-2 border border-t-0 border-gray-100 rounded-bl-lg rounded-br-lg">
            <div className="flex-[0.3] font-semibold p-4">
              Promise to provide date
            </div>
            <div className="flex-1 flex flex-col space-y-4 p-4">
              <span className="mb-2 font-semibold">Target</span>
              <div className="flex">
                <span className="flex-1">Promise to provide</span>
                <span
                  className="flex-[0.1] text-center"
                  dangerouslySetInnerHTML={{
                    __html: getTargetStatus(
                      info?.promise_to_provide_date?.target?.promise_to_provide
                    ),
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default PromiseToProvideInformation;
