apiVersion: apps/v1
kind: Deployment
metadata:
  name: ds-frontend
  namespace: staging
  labels:
    app: ds-frontend

spec:
  replicas: 1
  selector:
    matchLabels:
      app: ds-frontend
  
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%

  template:
    metadata:
      name: ds-frontend
      labels:
        app: ds-frontend
    
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: staging
                operator: In
                values:
                - "true"
      containers:
      - name: ds-frontend
        image: DOCKER_IMAGE
        # volumeMounts:
        # - name: ds-file
        #   mountPath: /app/.env
        #   subPath: .env
        # envFrom:
        # - configMapRef:
        #     name: frontend-config
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: ds-frontend
        resources:
          requests:
            memory: "300Mi"
            cpu: "256m"
          limits:
            memory: "600Mi"
            cpu: "300m"
      # volumes:
      # - name: ds-file
      #   configMap:
      #     name: frontend-config
