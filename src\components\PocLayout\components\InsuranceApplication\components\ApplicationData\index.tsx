import React, { useEffect, useRef, useState } from "react";
import { ApplicationDataProps, tab } from "./typings";
import { convertToSentenceCase } from "@utils";
import cx from "classnames";
import InsuredInformation from "./InsuredInformation";
import PolicyInformation from "./PolicyInformation";
import PaymentInformation from "./PaymentInformation";
import LineOfBusiness from "./LineOfBusiness";
import DriverInformation from "./DriverInformation";
import VehicleInformation from "./VehicleInformation";
import CoverageInformation from "./CoverageInformation";
import CompanyInformation from "./CompanyInformation";
import Signature from "./Signature";
import PromiseToProvideInformation from "./PromiseToProvideInformation";
import { InsuranceApplicationResponse } from "../../typings";
import { tabData } from "./tabs";
import { motion } from "framer-motion";
import { Arrow } from "@public/assets/icons";

const displayTabData = (
  active: string,
  data: InsuranceApplicationResponse,
  currentIndex: number,
  handlePreviousIndex: (event: React.MouseEvent<HTMLButtonElement>) => void,
  handleNextIndex: (event: React.MouseEvent<HTMLButtonElement>) => void
) => {
  switch (active) {
    case tab.insured:
      return <InsuredInformation info={data?.insured_information} />;
    case tab.policy:
      return <PolicyInformation info={data?.policy_information} />;
    case tab.payment:
      return <PaymentInformation info={data?.payment_information} />;
    case tab.business:
      return <LineOfBusiness info={data?.line_of_business} />;
    case tab.driver:
      return (
        <DriverInformation
          info={data?.driver_information}
          currentDriverIndex={currentIndex}
        />
      );
    case tab.vehicle:
      return <VehicleInformation info={data?.vehicle_information} />;
    case tab.coverage:
      return (
        <CoverageInformation
          info={data?.coverage_information}
          currentVehicleIndex={currentIndex}
          handlePreviousIndex={handlePreviousIndex}
          handleNextIndex={handleNextIndex}
        />
      );
    case tab.company:
      return <CompanyInformation info={data?.company_information} />;
    case tab.signature:
      return <Signature info={data?.signature} />;
    case tab.p2p:
      return (
        <PromiseToProvideInformation
          info={data?.promise_to_provide_information}
        />
      );
  }
};
const ApplicationData: React.FC<ApplicationDataProps> = ({
  data,
  hasResult,
}) => {
  const [activeTab, setActiveTab] = useState<string>(tab.insured);
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [scrollPosition, setScrollPosition] = useState<number>(0);
  const parentRef = useRef<HTMLDivElement>(null);
  const scrollRef = useRef<HTMLDivElement>(null);
  const totalDrivers = data?.driver_information?.drivers;
  const showDriverNavigation =
    totalDrivers?.length > 1 && activeTab === tab.driver;

  useEffect(() => {
    const handleResize = () => {
      const isTablet = window.innerWidth <= 1300;
      setScrollPosition(isTablet ? 800 : 500);
    };

    if (hasResult) {
      if (scrollRef && scrollRef.current) {
        window.scrollTo(0, scrollRef.current.offsetTop + scrollPosition);
      }
    }

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [hasResult, scrollRef, scrollPosition]);

  useEffect(() => {
    if (parentRef && parentRef.current) {
      if (activeTab !== tab.coverage) {
        parentRef.current.scrollTo(0, 0);
      }
    }
  }, [hasResult, activeTab, currentIndex]);

  const handlePreviousIndex = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    if (currentIndex !== 0) {
      setCurrentIndex((prevDriverIndex: number) => prevDriverIndex - 1);
    }
  };

  const handleNextIndex = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    if (currentIndex !== totalDrivers?.length - 1) {
      setCurrentIndex((prevDriverIndex: number) => prevDriverIndex + 1);
    }
  };

  useEffect(() => {
    setCurrentIndex(0);
  }, [activeTab]);

  return (
    <motion.div
      ref={scrollRef}
      className="relative mx-auto flex flex-col lg:flex-row rounded-lg py-4 pr-2 bshadow-secondary-light"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <div className="flex flex-row gap-x-3 lg:gap-x-0 lg:flex-col py-6 pr-6 gap-y-3 flex-wrap">
        {tabData.map(({ id, tab }) => (
          <React.Fragment key={id}>
            <motion.div
              whileTap={{ scale: 0.9, transition: { duration: 0.2 } }}
              onClick={() => setActiveTab(tab)}
              className={cx(
                "focus:outline-none w-max lg:w-[370px] flex items-center p-4 font-semibold cursor-pointer text-lg hover:bg-brand-secondary/10 transition duration-150 rounded-tr-xl rounded-br-xl",
                {
                  "bg-brand-secondary/70 text-white hover:bg-brand-secondary/70 shadow-md shadow-brand-secondary/30":
                    tab === activeTab,
                }
              )}
            >
              <span className="pl-4">{convertToSentenceCase(tab)}</span>
            </motion.div>
            <span className="flex items-center lg:hidden border-r"></span>
          </React.Fragment>
        ))}
      </div>
      <div
        className="py-6 px-8 flex-1 lg:w-[800px] max-h-[750px] c-scroll overflow-y-scroll overflow-x-hidden"
        ref={parentRef}
      >
        {displayTabData(
          activeTab,
          data,
          currentIndex,
          handlePreviousIndex,
          handleNextIndex
        )}
      </div>
      {showDriverNavigation && (
        <>
          <motion.button
            className="flex items-center justify-center w-10 text-white h-10 bg-brand-secondary/70 rounded-full absolute z-[999] top-[350px] left-[400px] navigation-left focus:outline-none border-0 disabled:bg-gray-200 pointer-events-auto"
            onClick={handlePreviousIndex}
            initial={{ opacity: 0, scale: 1 }}
            animate={{ opacity: 1 }}
            whileTap={currentIndex !== 0 ? { scale: 0.8 } : {}}
            transition={{ duration: 0.2 }}
            disabled={currentIndex === 0}
          >
            <Arrow />
          </motion.button>
          <motion.button
            className="flex items-center justify-center w-10 h-10 text-white bg-brand-secondary/70 rounded-full absolute z-[999] top-[350px] right-[20px] -translate-y-1/2 navigation-right focus:outline-none border-0 disabled:bg-gray-200 pointer-events-auto"
            onClick={handleNextIndex}
            initial={{ opacity: 0, scale: 1 }}
            animate={{ opacity: 1 }}
            whileTap={
              currentIndex !== totalDrivers?.length - 1 ? { scale: 0.8 } : {}
            }
            transition={{ duration: 0.2 }}
            disabled={currentIndex === totalDrivers?.length - 1}
          >
            <Arrow />
          </motion.button>
        </>
      )}
    </motion.div>
  );
};

export default ApplicationData;
