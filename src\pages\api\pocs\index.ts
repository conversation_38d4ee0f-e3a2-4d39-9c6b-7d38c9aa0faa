import { NextApiRequest, NextApiResponse } from "next";
import pocData from "@public/assets/json/data.json";

type PocObj = {
  id: number;
  src: string;
  title: string;
  slug: string;
  subtitle: string;
};

type ResponseData = {
  data: PocObj[];
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ResponseData>
) {
  res.status(200).json({ data: pocData.data });
}
