export enum doctypes {
  required = "required",
  optional = "optional",
}
export enum docIds {
  license = "driving_license",
  registration = "registration",
}
export const requiredProperties = [
  "driving_license",
  "itc",
  "crm_receipt",
  "application",
  "motor_vehicle_record",
  "broker_package",
];
export const optionalProperties = [
  "pleasure_use_letter",
  "artisan_use_letter",
  "eft",
  "non_owners_letter",
  "stripe_receipt",
  "registration",
  "vehicle_record",
  "promise_to_provide",
];
export const uploadDocuments = {
  required: [
    {
      id: "driving_license",
      title: "Driving License",
      type: "required",
    },
    {
      id: "itc",
      title: "ITC",
      type: "required",
    },
    {
      id: "crm_receipt",
      title: "CRM Receipt",
      type: "required",
    },
    {
      id: "application",
      title: "Application",
      type: "required",
    },
    {
      id: "motor_vehicle_record",
      title: "Motor Vehicle Record",
      type: "required",
    },
    {
      id: "broker_package",
      title: "Broker Package",
      type: "required",
    },
  ],
  optional: [
    {
      id: "pleasure_use_letter",
      title: "Pleasure Use Letter",
      type: "optional",
    },
    {
      id: "artisan_use_letter",
      title: "Artisan Use Letter",
      type: "optional",
    },
    {
      id: "eft",
      title: "EFT",
      type: "optional",
    },
    {
      id: "non_owners_letter",
      title: "Non Owners Letter",
      type: "optional",
    },
    {
      id: "stripe_receipt",
      title: "Stripe Receipt",
      type: "optional",
    },
    {
      id: "registration",
      title: "Registration",
      type: "optional",
    },
    {
      id: "vehicle_record",
      title: "Vehicle Record",
      type: "optional",
    },
    {
      id: "promise_to_provide",
      title: "Promise To Provide",
      type: "optional",
    },
  ],
};
