import React from "react";
import { motion } from "framer-motion";
import { convertToSentenceCase, getTargetStatus } from "@utils";

interface SignatureProps {
  info: any;
}

const isPropertyNull = (property: any): boolean => {
  return property === null || property === undefined;
};

const Signature: React.FC<SignatureProps> = ({ info }) => {
  return (
    <motion.div
      className="relative flex flex-col space-y-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <section>
        <div className="bg-brand-secondary/10 text-brand-secondary/75 font-semibold text-lg rounded-tl-lg rounded-tr-lg px-4 py-3">
          Insurance application
        </div>
        <div className="shadow-md rounded-lg">
          <div className="flex flex-col space-y-6 border border-t-0 p-4 rounded-br-lg rounded-bl-lg">
            <div className="flex items-center justify-between font-semibold">
              <div>Title</div>
              <div className="flex gap-x-8">
                <span className="w-[100px] text-center">Is signed?</span>
                <span className="w-[100px] text-center">Is dated?</span>
              </div>
            </div>
            {info &&
              info.signature &&
              info.signature.insurance_application &&
              Object.entries(info.signature.insurance_application).map(
                ([key, value]: any) => (
                  <div className="flex items-center justify-between" key={key}>
                    <div>{convertToSentenceCase(key)}</div>
                    <div className="flex gap-x-8">
                      <span
                        className="w-[100px] text-center"
                        dangerouslySetInnerHTML={{
                          __html: getTargetStatus(value?.is_signed),
                        }}
                      />
                      <span
                        className="w-[100px] text-center"
                        dangerouslySetInnerHTML={{
                          __html: getTargetStatus(value?.is_dated),
                        }}
                      />
                    </div>
                  </div>
                )
              )}
          </div>
        </div>
      </section>
      <section>
        <div className="bg-brand-secondary/10 text-brand-secondary/75 font-semibold text-lg rounded-tl-lg rounded-tr-lg px-4 py-3">
          ITC
        </div>
        <div className="shadow-md rounded-lg">
          <div className="flex flex-col space-y-6 border border-t-0 p-4 rounded-br-lg rounded-bl-lg">
            <div className="flex items-center justify-between font-semibold">
              <div>Title</div>
              <span className="w-[100px] text-center">Is signed?</span>
            </div>
            {info &&
              info.signature &&
              info.signature.itc &&
              Object.entries(info.signature.itc).map(([key, value]: any) => (
                <div className="flex items-center justify-between" key={key}>
                  <div>{convertToSentenceCase(key)}</div>
                  <span
                    className="w-[100px] text-center"
                    dangerouslySetInnerHTML={{
                      __html: getTargetStatus(value?.is_signed),
                    }}
                  />
                </div>
              ))}
          </div>
        </div>
      </section>
      <section>
        <div className="bg-brand-secondary/10 text-brand-secondary/75 font-semibold text-lg rounded-tl-lg rounded-tr-lg px-4 py-3">
          Broker package
        </div>
        <div className="shadow-md rounded-lg">
          <div className="flex flex-col space-y-4 border border-t-0 p-4 rounded-br-lg rounded-bl-lg">
            <span className="font-semibold">Disclosures</span>
            <div className="flex flex-col space-y-4 bshadow p-4 rounded-lg">
              <div className="flex items-center justify-between font-semibold">
                <div>Title</div>
                <span className="w-[100px] text-center">Is signed?</span>
              </div>
              <div className="flex items-center justify-between">
                <div>Coverages</div>
                <span
                  className="w-[100px] text-center"
                  dangerouslySetInnerHTML={{
                    __html: getTargetStatus(
                      info?.signature?.broker_package?.disclosures?.coverages
                        ?.is_signed
                    ),
                  }}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>Disclosure of driving record</div>
                <span
                  className="w-[100px] text-center"
                  dangerouslySetInnerHTML={{
                    __html: getTargetStatus(
                      info?.signature?.broker_package?.disclosures
                        ?.disclosure_of_driving_record?.is_signed
                    ),
                  }}
                />
              </div>
              <span className="font-semibold">Exclusion</span>
              <div className="flex flex-col space-y-4 bshadow p-4 rounded-lg">
                <div className="flex items-center justify-between font-semibold">
                  <div>Title</div>
                  <span className="w-[100px] text-center">Is signed?</span>
                </div>
                <div className="flex items-center justify-between">
                  <div>Uninsured bi or pd coverage</div>
                  <span
                    className="w-[100px] text-center"
                    dangerouslySetInnerHTML={{
                      __html: getTargetStatus(
                        info?.signature?.broker_package?.disclosures?.exclusion
                          ?.uninsured_bi_or_pd_coverage?.is_signed
                      ),
                    }}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>Comprehensive and collision coverage</div>
                  <span
                    className="w-[100px] text-center"
                    dangerouslySetInnerHTML={{
                      __html: getTargetStatus(
                        info?.signature?.broker_package?.disclosures?.exclusion
                          ?.comprehensive_and_collision_coverage?.is_signed
                      ),
                    }}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>Business or commercial use</div>
                  <span
                    className="w-[100px] text-center"
                    dangerouslySetInnerHTML={{
                      __html: getTargetStatus(
                        info?.signature?.broker_package?.disclosures?.exclusion
                          ?.business_or_commercial_use?.is_signed
                      ),
                    }}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>Named drivers limitation</div>
                  <span
                    className="w-[100px] text-center"
                    dangerouslySetInnerHTML={{
                      __html: getTargetStatus(
                        info?.signature?.broker_package?.disclosures?.exclusion
                          ?.named_drivers_limitation?.is_signed
                      ),
                    }}
                  />
                </div>
              </div>
            </div>
            <div className="flex items-center justify-between border p-4 rounded-lg shadow-md">
              <span>Standard broker fee disclosure</span>
              <div className="flex flex-col gap-y-4">
                <span className="w-[100px] text-center font-semibold">
                  Is signed?
                </span>
                <span
                  className="w-[100px] text-center"
                  dangerouslySetInnerHTML={{
                    __html: getTargetStatus(
                      info?.signature?.broker_package
                        ?.standard_broker_fee_disclosure?.is_signed
                    ),
                  }}
                />
              </div>
            </div>
            <div className="flex items-center justify-between border p-4 rounded-lg shadow-md">
              <span>Text messaging consent agreement</span>
              <div className="flex flex-col gap-y-4">
                <span className="w-[100px] text-center font-semibold">
                  Is signed?
                </span>
                <span
                  className="w-[100px] text-center"
                  dangerouslySetInnerHTML={{
                    __html: getTargetStatus(
                      info?.signature?.broker_package
                        ?.text_messaging_consent_agreement?.is_signed
                    ),
                  }}
                />
              </div>
            </div>
            <span className="font-semibold">Broker fee agreement</span>
            <div className="flex flex-col space-y-4 bshadow p-4 rounded-lg">
              <div className="flex items-center justify-between font-semibold">
                <div>Title</div>
                <span className="w-[100px] text-center">Is signed?</span>
              </div>
              <div className="flex items-center justify-between">
                <div>Client initials</div>
                <span
                  className="w-[100px] text-center"
                  dangerouslySetInnerHTML={{
                    __html: getTargetStatus(
                      info?.signature?.broker_package?.broker_fee_agreement
                        ?.client_initials?.is_signed
                    ),
                  }}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>Condition and acknowledgement agreement</div>
                <span
                  className="w-[100px] text-center"
                  dangerouslySetInnerHTML={{
                    __html: getTargetStatus(
                      info?.signature?.broker_package?.broker_fee_agreement
                        ?.condition_and_acknowledgement_agreement?.is_signed
                    ),
                  }}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>Client signature</div>
                <span
                  className="w-[100px] text-center"
                  dangerouslySetInnerHTML={{
                    __html: getTargetStatus(
                      info?.signature?.broker_package?.broker_fee_agreement
                        ?.client_signature?.is_signed
                    ),
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </section>
      {isPropertyNull(info?.signature?.artisan_use_letter) ? (
        <div className="flex p-4 items-center justify-between border rounded-lg shadow-md">
          <div className="font-semibold">Artisan use letter</div>
          <span>-</span>
        </div>
      ) : (
        <div className="flex items-center justify-between border p-4 rounded-lg shadow-md">
          <span>Artisan use letter</span>
          <div className="flex flex-col gap-y-4">
            <span className="w-[100px] text-center font-semibold">
              Is signed?
            </span>
            <span
              className="w-[100px] text-center"
              dangerouslySetInnerHTML={{
                __html: getTargetStatus(
                  info?.signature?.artisan_use_letter?.is_signed
                ),
              }}
            />
          </div>
        </div>
      )}
      {isPropertyNull(info?.signature?.non_owners_letter) ? (
        <div className="flex p-4 items-center justify-between border rounded-lg shadow-md">
          <div className="font-semibold">Non owners letter</div>
          <span>-</span>
        </div>
      ) : (
        <div className="flex items-center justify-between border p-4 rounded-lg shadow-md">
          <span>Non owners letter</span>
          <div className="flex flex-col gap-y-4">
            <span className="w-[100px] text-center font-semibold">
              Is signed?
            </span>
            <span
              className="w-[100px] text-center"
              dangerouslySetInnerHTML={{
                __html: getTargetStatus(
                  info?.signature?.non_owners_letter?.is_signed
                ),
              }}
            />
          </div>
        </div>
      )}
      {isPropertyNull(info?.signature?.eft) ? (
        <div className="flex p-4 items-center justify-between border rounded-lg shadow-md">
          <div className="font-semibold">EFT</div>
          <span>-</span>
        </div>
      ) : (
        <section>
          <div className="bg-brand-secondary/10 text-brand-secondary/75 font-semibold text-lg rounded-tl-lg rounded-tr-lg px-4 py-3">
            EFT
          </div>
          <div className="shadow-md rounded-lg">
            <div className="flex flex-col space-y-6 border border-t-0 p-4 rounded-br-lg rounded-bl-lg">
              <div className="flex items-center justify-between font-semibold">
                <div>Title</div>
                <div className="flex gap-x-8">
                  <span className="w-[100px] text-center">Is signed?</span>
                  <span className="w-[100px] text-center">Is dated?</span>
                </div>
              </div>
              {info &&
                info.signature &&
                info.signature.eft &&
                Object.entries(info.signature.eft).map(([key, value]: any) => (
                  <div className="flex items-center justify-between" key={key}>
                    <div>{convertToSentenceCase(key)}</div>
                    <div className="flex gap-x-8">
                      <span
                        className="w-[100px] text-center"
                        dangerouslySetInnerHTML={{
                          __html: getTargetStatus(value?.is_signed),
                        }}
                      />
                      <span
                        className="w-[100px] text-center"
                        dangerouslySetInnerHTML={{
                          __html: getTargetStatus(value?.is_dated),
                        }}
                      />
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </section>
      )}
      {isPropertyNull(info?.signature?.pleasure_use_letter) ? (
        <div className="flex p-4 items-center justify-between border rounded-lg shadow-md">
          <div className="font-semibold">Pleasure use letter</div>
          <span>-</span>
        </div>
      ) : (
        <div className="flex items-center justify-between border p-4 rounded-lg shadow-md">
          <span>Pleasure use letter</span>
          <div className="flex flex-col gap-y-4">
            <span className="w-[100px] text-center font-semibold">
              Is signed?
            </span>
            <span
              className="w-[100px] text-center"
              dangerouslySetInnerHTML={{
                __html: getTargetStatus(
                  info?.signature?.pleasure_use_letter?.is_signed
                ),
              }}
            />
          </div>
        </div>
      )}
      {isPropertyNull(info?.signature?.promise_to_provide) ? (
        <div className="flex p-4 items-center justify-between border rounded-lg shadow-md">
          <div className="font-semibold">Non owners letter</div>
          <span>-</span>
        </div>
      ) : (
        <section>
          <div className="bg-brand-secondary/10 text-brand-secondary/75 font-semibold text-lg rounded-tl-lg rounded-tr-lg px-4 py-3">
            Promise to provide
          </div>
          <div className="shadow-md rounded-lg">
            <div className="flex flex-col space-y-6 border border-t-0 p-4 rounded-br-lg rounded-bl-lg">
              <div className="flex items-center justify-between font-semibold">
                <div>Title</div>
                <span className="w-[100px] text-center">Is signed?</span>
              </div>
              {info &&
                info.signature &&
                info.signature.promise_to_provide &&
                Object.entries(info.signature.promise_to_provide).map(
                  ([key, value]: any) => (
                    <div
                      className="flex items-center justify-between"
                      key={key}
                    >
                      <div>{convertToSentenceCase(key)}</div>
                      <span
                        className="w-[100px] text-center"
                        dangerouslySetInnerHTML={{
                          __html: getTargetStatus(value?.is_signed),
                        }}
                      />
                    </div>
                  )
                )}
            </div>
          </div>
        </section>
      )}
      <div className="flex items-center justify-between border p-4 rounded-lg shadow-md">
        <span>MVR</span>
        <div className="flex flex-col gap-y-4">
          <span className="w-[100px] text-center font-semibold">
            Is signed?
          </span>
          <span
            className="w-[100px] text-center"
            dangerouslySetInnerHTML={{
              __html: getTargetStatus(info?.signature?.mvr?.is_signed),
            }}
          />
        </div>
      </div>
    </motion.div>
  );
};

export default Signature;
