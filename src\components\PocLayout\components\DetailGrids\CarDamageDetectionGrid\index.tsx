import React from "react";
import { CarDamageDetails, CarDamageDetectionGridProps } from "./typings";
import { convertToSentenceCase } from "@utils";

const CarDamageDetectionGrid: React.FC<CarDamageDetectionGridProps> = ({
  details,
  filePath,
}) => {
  const currentDetails =
    details &&
    details.find((item: CarDamageDetails) => item.file_path === filePath);
  return (
    <>
      <span className="text-lg font-bold pb-3">Confidence percentage</span>
      <div className="flex flex-col divide-y-2 divide-brand-white/90">
        {currentDetails &&
          Object.entries(currentDetails?.results).map(
            ([key, value]: [string, any]) => (
              <div className="flex justify-between items-center py-3" key={key}>
                <span className="flex-1 text-gradient-black font-semibold">
                  {convertToSentenceCase(key)}
                </span>
                <span className="flex items-center space-x-4 flex-1 text-gradient-gray overflow-hidden">
                  <meter max={100} value={value} className="w-full flex-1" />
                  <span className="flex-[0.2]">{value}%</span>
                </span>
              </div>
            )
          )}
      </div>
    </>
  );
};

export default CarDamageDetectionGrid;
