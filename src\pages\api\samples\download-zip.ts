import {
  S3Client,
  ListObjectsV2Command,
  GetObjectCommand,
  ListObjectsV2CommandInput,
} from "@aws-sdk/client-s3";
import { s3Values } from "@constants/s3";
import archiver from "archiver";
import { NextApiRequest, NextApiResponse } from "next";
import { Readable } from "stream";

const s3 = new S3Client({
  region: s3Values.region,
});

async function downloadAndAddToZip(
  archive: archiver.Archiver,
  objectKey: string,
  bucketName: string,
  directoryPrefix: string
): Promise<void> {
  const getObjectParams = {
    Bucket: bucketName,
    Key: objectKey,
    SSECustomerAlgorithm: "AES256",
    SSECustomerKey: s3Values.sseCustomerKey,
  };
  const { Body } = await s3.send(new GetObjectCommand(getObjectParams));
  const relativePath = objectKey.replace(`${directoryPrefix}/`, "");
  archive.append(Body as Readable, { name: relativePath });
}

async function downloadDirectory(
  archive: archiver.Archiver,
  directoryPrefix: string,
  bucketName: string
): Promise<void> {
  let continuationToken: string | undefined = undefined;

  do {
    const listObjectsParams: ListObjectsV2CommandInput = {
      Bucket: bucketName,
      Prefix: directoryPrefix,
      ContinuationToken: continuationToken,
    };

    const { Contents, NextContinuationToken } = await s3.send(
      new ListObjectsV2Command(listObjectsParams)
    );

    for (const object of Contents || []) {
      if (!object.Key!.endsWith("/")) {
        await downloadAndAddToZip(
          archive,
          object.Key!,
          bucketName,
          directoryPrefix
        );
      }
    }

    continuationToken = NextContinuationToken;
  } while (continuationToken);
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const bucketName = String(req.query.bucketName);
  const directoryPrefix = String(req.query.prefix);
  const zipFileName = String(req.query.folderName);

  try {
    const archive = archiver("zip", { zlib: { level: 9 } });

    res.setHeader("Content-Type", "application/zip");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename="${zipFileName}"`
    );

    archive.pipe(res);

    await downloadDirectory(archive, directoryPrefix, bucketName);

    archive.finalize();
  } catch (error) {
    console.error("Error:", error);
    res.status(500).json({ error: "Error creating zip file" });
  }
}
