//Envs
export const API_DOMAIN = process.env.NEXT_PUBLIC_API_DOMAIN || "";
export const INSURANCE_TOKEN = process.env.NEXT_PUBLIC_INSURANCE_TOKEN || "";
export const AMD_TOKEN = process.env.NEXT_PUBLIC_AMD_TOKEN || "";
export const MAX_FILE_SIZE =
  parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE!, 10) || 0;
export const MAX_PAGES_LIMIT =
  parseInt(process.env.NEXT_PUBLIC_MAX_PAGES_LIMIT!, 10) || 0;
export const DEFAULT_PASSWORD =
  process.env.NEXT_PUBLIC_AWS_COGNITO_TEMP_PASS || "";

//Logo
export const LOGO_ALT = "Maruti Techlabs";

//Regex
export const EMAIL_REGEX =
  /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

//Errors
export const EMPTY_EMAIL = "Please enter an email";
export const INVALID_EMAIL = "Please enter a valid email";
export const EMPTY_PASSWORD = "Please enter a password";
export const INVALID_PASSWORD =
  "Password length must be atleast minimum 8 characters";
export const SOMETHING_WENT_WRONG = "Something went wrong!";
export const UPLOAD_ERROR = "An error occurred while uploading the file.";

//Titles
export const AMD_TITLE = "Answering machine detection";

//AWS Sign In Steps
export const NEW_PASSWORD_STEP = "CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED";
