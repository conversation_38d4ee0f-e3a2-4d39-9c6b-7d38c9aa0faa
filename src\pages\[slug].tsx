import { Loader, PocLayout } from "@components";
import { slugs } from "@constants/slugs";
import { AMD_TITLE } from "@constants";
import { convertToSentenceCase, getSubtitle } from "@utils";
import Head from "next/head";
import { useRouter } from "next/router";
import React from "react";
import { useAuthState } from "@contexts/AuthContext";

const PocPage: React.FC = () => {
  const router = useRouter();
  const slug = router.asPath.replace("/", "");
  const { user } = useAuthState();
  const isAMD = slug === slugs.amd;
  if (!user) {
    return <Loader />;
  } else {
    return (
      <>
        <Head>
          <title>
            {isAMD ? AMD_TITLE : convertToSentenceCase(slug)} | Maruti Techlabs
          </title>
        </Head>
        <PocLayout
          slug={slug}
          subtitle={getSubtitle(slug).subtitle}
          keyword={getSubtitle(slug).keyword}
        />
      </>
    );
  }
};

export default PocPage;
