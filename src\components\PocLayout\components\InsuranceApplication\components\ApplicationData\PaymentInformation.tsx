import React from "react";
import { motion } from "framer-motion";
import { convertToSentenceCase, getTargetStatus } from "@utils";

interface PaymentInformationProps {
  info: any;
}
const CommonTable: React.FC<{
  source: any;
  title: string;
  targetTitle: string;
  target: boolean | null;
}> = ({ source, title, targetTitle, target }) => {
  if (source === null || source === undefined) {
    return (
      <div className="flex p-4 items-center justify-between border rounded-lg shadow-md">
        <span>{title}</span>
        <span>-</span>
      </div>
    );
  } else {
    return (
      <div className="shadow-md rounded-lg">
        <div className="bg-gray-100 p-4 rounded-tr-lg rounded-tl-lg">
          Source:{" "}
          <span className="font-semibold">
            {convertToSentenceCase(source?.source) || "-"}
          </span>
        </div>
        <div className="flex p-2 border border-t-0 border-gray-100 rounded-bl-lg rounded-br-lg">
          <div className="flex-[0.3] font-semibold p-4">{title}</div>
          <div className="flex-1 flex flex-col space-y-4 p-4">
            <span className="mb-2 font-semibold">Target</span>
            <div className="flex">
              <span className="flex-1">{targetTitle}</span>
              <span
                className="flex-[0.1] text-center"
                dangerouslySetInnerHTML={{
                  __html: getTargetStatus(target),
                }}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }
};
const PaymentInformation: React.FC<PaymentInformationProps> = ({ info }) => {
  return (
    <motion.div
      className="flex-1 flex flex-col space-y-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <CommonTable
        source={info?.payment_date}
        title="Payment date"
        targetTitle="CRM receipt"
        target={info?.payment_date?.target?.crm_receipt}
      />
      <CommonTable
        source={info?.down_payment}
        title="Down payment"
        targetTitle="CRM receipt"
        target={info?.down_payment?.target?.crm_receipt}
      />
      <div className="flex p-4 items-center justify-between border rounded-lg shadow-md">
        <span>VR fee</span>
        <span>{info?.vr_fee || "-"}</span>
      </div>
      <CommonTable
        source={info?.broker_fee}
        title="Broker fee"
        targetTitle="Broker package"
        target={info?.broker_fee?.target?.broker_package}
      />
      <CommonTable
        source={info?.reference_number}
        title="Reference number"
        targetTitle="CRM receipt"
        target={info?.reference_number?.target?.crm_receipt}
      />
      <CommonTable
        source={info?.amount_paid}
        title="Amount paid"
        targetTitle="CRM receipt"
        target={info?.amount_paid?.target?.crm_receipt}
      />
      <CommonTable
        source={info?.payment_method}
        title="Payment method"
        targetTitle="CRM receipt"
        target={info?.payment_method?.target?.crm_receipt}
      />
    </motion.div>
  );
};

export default PaymentInformation;
