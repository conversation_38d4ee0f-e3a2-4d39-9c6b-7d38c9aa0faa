import { NextApiRequest, NextApiResponse } from "next";
import pocData from "@public/assets/json/data.json";
type ResponseData = {
  id: number;
  src: string;
  title: string;
  slug: string;
  subtitle: string;
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ResponseData | undefined>
) {
  const pocObj = pocData.data.find(
    (poc: ResponseData) => poc.slug === req.query.slug
  );
  return res.status(200).json(pocObj);
}
