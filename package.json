{"name": "maruti-ds-demo-site", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.569.0", "@fortawesome/fontawesome-free": "^6.5.1", "@types/archiver": "^6.0.2", "@types/formidable": "^3.4.5", "@types/lodash": "^4.14.202", "archiver": "^6.0.1", "aws-amplify": "^6.3.2", "formidable": "^3.5.1", "formik": "^2.4.5", "framer-motion": "^10.16.16", "hover.css": "^2.3.2", "lodash": "^4.17.21", "next": "14.0.3", "react": "^18", "react-dom": "^18", "react-pdf": "^7.7.1", "sharp": "^0.33.2", "yup": "^1.3.2"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "classnames": "^2.3.2", "eslint": "^8", "eslint-config-next": "14.0.3", "postcss": "^8", "sass": "^1.69.5", "tailwindcss": "^3.3.0", "typescript": "^5"}}