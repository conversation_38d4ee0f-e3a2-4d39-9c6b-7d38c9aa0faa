import React from "react";
import { AMDGridProps } from "./typings";
import { getTargetStatus } from "@utils";

const CommonFlexContainer: React.FC<{
  title: string;
  value: string | boolean;
}> = ({ title, value }) => {
  return (
    <div className="flex justify-between items-center py-3">
      <span className="flex-1 text-gradient-black font-semibold">{title}</span>
      <span
        className="flex-1 text-gradient-gray"
        dangerouslySetInnerHTML={{
          __html:
            typeof value === "boolean" ? getTargetStatus(value) : value || "NA",
        }}
      />
    </div>
  );
};
const AMDGrid: React.FC<AMDGridProps> = ({ details }) => {
  return (
    <div className="flex flex-col divide-y-2 divide-brand-white/90">
      <CommonFlexContainer
        title="Is human answer?:"
        value={details?.is_human_answer}
      />
      <CommonFlexContainer
        title="Input audio length:"
        value={details?.input_audio_length}
      />
    </div>
  );
};

export default AMDGrid;
